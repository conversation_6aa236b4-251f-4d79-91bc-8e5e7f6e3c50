// CIH99 Content Protection Layer
const CIH99_CONTENT_GUARD = {
  config: {
    key: 'Q0lIOTlfVVRJTF9TVEFURTk5'
  },
  decode: (s) => { try { return atob(s); } catch(e) { return ''; } },
  isReady: false,
  async init() {
    try {
      const result = await chrome.storage.local.get(this.decode(this.config.key));
      this.isReady = result[this.decode(this.config.key)]?.isReady || false;
    } catch(e) { this.isReady = false; }
  },
  check() { return this.isReady; },
  guard(fn) { return this.isReady ? fn() : console.warn('CIH99: Content blocked - system not ready'); }
};
CIH99_CONTENT_GUARD.init();

(() => {
  var e = {
      733: (e) => {
        e.exports = (function e(t, r, n) {
          function i(o, a) {
            if (!r[o]) {
              if (!t[o]) {
                if (s) return s(o, !0);
                var l = new Error("Cannot find module '" + o + "'");
                throw ((l.code = "MODULE_NOT_FOUND"), l);
              }
              var d = (r[o] = { exports: {} });
              t[o][0].call(
                d.exports,
                function (e) {
                  return i(t[o][1][e] || e);
                },
                d,
                d.exports,
                e,
                t,
                r,
                n,
              );
            }
            return r[o].exports;
          }
          for (var s = void 0, o = 0; o < n.length; o++) i(n[o]);
          return i;
        })(
          {
            1: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = e("./support"),
                  s =
                    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
                ((r.encode = function (e) {
                  for (
                    var t,
                      r,
                      i,
                      o,
                      a,
                      l,
                      d,
                      c = [],
                      u = 0,
                      h = e.length,
                      f = h,
                      m = "string" !== n.getTypeOf(e);
                    u < e.length;

                  )
                    ((f = h - u),
                      (i = m
                        ? ((t = e[u++]),
                          (r = u < h ? e[u++] : 0),
                          u < h ? e[u++] : 0)
                        : ((t = e.charCodeAt(u++)),
                          (r = u < h ? e.charCodeAt(u++) : 0),
                          u < h ? e.charCodeAt(u++) : 0)),
                      (o = t >> 2),
                      (a = ((3 & t) << 4) | (r >> 4)),
                      (l = 1 < f ? ((15 & r) << 2) | (i >> 6) : 64),
                      (d = 2 < f ? 63 & i : 64),
                      c.push(
                        s.charAt(o) + s.charAt(a) + s.charAt(l) + s.charAt(d),
                      ));
                  return c.join("");
                }),
                  (r.decode = function (e) {
                    var t,
                      r,
                      n,
                      o,
                      a,
                      l,
                      d = 0,
                      c = 0,
                      u = "data:";
                    if (e.substr(0, u.length) === u)
                      throw new Error(
                        "Invalid base64 input, it looks like a data url.",
                      );
                    var h,
                      f =
                        (3 * (e = e.replace(/[^A-Za-z0-9+/=]/g, "")).length) /
                        4;
                    if (
                      (e.charAt(e.length - 1) === s.charAt(64) && f--,
                      e.charAt(e.length - 2) === s.charAt(64) && f--,
                      f % 1 != 0)
                    )
                      throw new Error(
                        "Invalid base64 input, bad content length.",
                      );
                    for (
                      h = i.uint8array
                        ? new Uint8Array(0 | f)
                        : new Array(0 | f);
                      d < e.length;

                    )
                      ((t =
                        (s.indexOf(e.charAt(d++)) << 2) |
                        ((o = s.indexOf(e.charAt(d++))) >> 4)),
                        (r =
                          ((15 & o) << 4) |
                          ((a = s.indexOf(e.charAt(d++))) >> 2)),
                        (n = ((3 & a) << 6) | (l = s.indexOf(e.charAt(d++)))),
                        (h[c++] = t),
                        64 !== a && (h[c++] = r),
                        64 !== l && (h[c++] = n));
                    return h;
                  }));
              },
              { "./support": 30, "./utils": 32 },
            ],
            2: [
              function (e, t, r) {
                "use strict";
                var n = e("./external"),
                  i = e("./stream/DataWorker"),
                  s = e("./stream/Crc32Probe"),
                  o = e("./stream/DataLengthProbe");
                function a(e, t, r, n, i) {
                  ((this.compressedSize = e),
                    (this.uncompressedSize = t),
                    (this.crc32 = r),
                    (this.compression = n),
                    (this.compressedContent = i));
                }
                ((a.prototype = {
                  getContentWorker: function () {
                    var e = new i(n.Promise.resolve(this.compressedContent))
                        .pipe(this.compression.uncompressWorker())
                        .pipe(new o("data_length")),
                      t = this;
                    return (
                      e.on("end", function () {
                        if (this.streamInfo.data_length !== t.uncompressedSize)
                          throw new Error(
                            "Bug : uncompressed data size mismatch",
                          );
                      }),
                      e
                    );
                  },
                  getCompressedWorker: function () {
                    return new i(n.Promise.resolve(this.compressedContent))
                      .withStreamInfo("compressedSize", this.compressedSize)
                      .withStreamInfo("uncompressedSize", this.uncompressedSize)
                      .withStreamInfo("crc32", this.crc32)
                      .withStreamInfo("compression", this.compression);
                  },
                }),
                  (a.createWorkerFrom = function (e, t, r) {
                    return e
                      .pipe(new s())
                      .pipe(new o("uncompressedSize"))
                      .pipe(t.compressWorker(r))
                      .pipe(new o("compressedSize"))
                      .withStreamInfo("compression", t);
                  }),
                  (t.exports = a));
              },
              {
                "./external": 6,
                "./stream/Crc32Probe": 25,
                "./stream/DataLengthProbe": 26,
                "./stream/DataWorker": 27,
              },
            ],
            3: [
              function (e, t, r) {
                "use strict";
                var n = e("./stream/GenericWorker");
                ((r.STORE = {
                  magic: "\0\0",
                  compressWorker: function () {
                    return new n("STORE compression");
                  },
                  uncompressWorker: function () {
                    return new n("STORE decompression");
                  },
                }),
                  (r.DEFLATE = e("./flate")));
              },
              { "./flate": 7, "./stream/GenericWorker": 28 },
            ],
            4: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = (function () {
                    for (var e, t = [], r = 0; r < 256; r++) {
                      e = r;
                      for (var n = 0; n < 8; n++)
                        e = 1 & e ? 3988292384 ^ (e >>> 1) : e >>> 1;
                      t[r] = e;
                    }
                    return t;
                  })();
                t.exports = function (e, t) {
                  return void 0 !== e && e.length
                    ? "string" !== n.getTypeOf(e)
                      ? (function (e, t, r, n) {
                          var s = i,
                            o = n + r;
                          e ^= -1;
                          for (var a = n; a < o; a++)
                            e = (e >>> 8) ^ s[255 & (e ^ t[a])];
                          return ~e;
                        })(0 | t, e, e.length, 0)
                      : (function (e, t, r, n) {
                          var s = i,
                            o = n + r;
                          e ^= -1;
                          for (var a = n; a < o; a++)
                            e = (e >>> 8) ^ s[255 & (e ^ t.charCodeAt(a))];
                          return ~e;
                        })(0 | t, e, e.length, 0)
                    : 0;
                };
              },
              { "./utils": 32 },
            ],
            5: [
              function (e, t, r) {
                "use strict";
                ((r.base64 = !1),
                  (r.binary = !1),
                  (r.dir = !1),
                  (r.createFolders = !0),
                  (r.date = null),
                  (r.compression = null),
                  (r.compressionOptions = null),
                  (r.comment = null),
                  (r.unixPermissions = null),
                  (r.dosPermissions = null));
              },
              {},
            ],
            6: [
              function (e, t, r) {
                "use strict";
                var n = null;
                ((n = "undefined" != typeof Promise ? Promise : e("lie")),
                  (t.exports = { Promise: n }));
              },
              { lie: 37 },
            ],
            7: [
              function (e, t, r) {
                "use strict";
                var n =
                    "undefined" != typeof Uint8Array &&
                    "undefined" != typeof Uint16Array &&
                    "undefined" != typeof Uint32Array,
                  i = e("pako"),
                  s = e("./utils"),
                  o = e("./stream/GenericWorker"),
                  a = n ? "uint8array" : "array";
                function l(e, t) {
                  (o.call(this, "FlateWorker/" + e),
                    (this._pako = null),
                    (this._pakoAction = e),
                    (this._pakoOptions = t),
                    (this.meta = {}));
                }
                ((r.magic = "\b\0"),
                  s.inherits(l, o),
                  (l.prototype.processChunk = function (e) {
                    ((this.meta = e.meta),
                      null === this._pako && this._createPako(),
                      this._pako.push(s.transformTo(a, e.data), !1));
                  }),
                  (l.prototype.flush = function () {
                    (o.prototype.flush.call(this),
                      null === this._pako && this._createPako(),
                      this._pako.push([], !0));
                  }),
                  (l.prototype.cleanUp = function () {
                    (o.prototype.cleanUp.call(this), (this._pako = null));
                  }),
                  (l.prototype._createPako = function () {
                    this._pako = new i[this._pakoAction]({
                      raw: !0,
                      level: this._pakoOptions.level || -1,
                    });
                    var e = this;
                    this._pako.onData = function (t) {
                      e.push({ data: t, meta: e.meta });
                    };
                  }),
                  (r.compressWorker = function (e) {
                    return new l("Deflate", e);
                  }),
                  (r.uncompressWorker = function () {
                    return new l("Inflate", {});
                  }));
              },
              { "./stream/GenericWorker": 28, "./utils": 32, pako: 38 },
            ],
            8: [
              function (e, t, r) {
                "use strict";
                function n(e, t) {
                  var r,
                    n = "";
                  for (r = 0; r < t; r++)
                    ((n += String.fromCharCode(255 & e)), (e >>>= 8));
                  return n;
                }
                function i(e, t, r, i, o, c) {
                  var u,
                    h,
                    f = e.file,
                    m = e.compression,
                    g = c !== a.utf8encode,
                    p = s.transformTo("string", c(f.name)),
                    y = s.transformTo("string", a.utf8encode(f.name)),
                    b = f.comment,
                    w = s.transformTo("string", c(b)),
                    v = s.transformTo("string", a.utf8encode(b)),
                    A = y.length !== f.name.length,
                    _ = v.length !== b.length,
                    x = "",
                    k = "",
                    S = "",
                    C = f.dir,
                    E = f.date,
                    O = { crc32: 0, compressedSize: 0, uncompressedSize: 0 };
                  (t && !r) ||
                    ((O.crc32 = e.crc32),
                    (O.compressedSize = e.compressedSize),
                    (O.uncompressedSize = e.uncompressedSize));
                  var I = 0;
                  (t && (I |= 8), g || (!A && !_) || (I |= 2048));
                  var T = 0,
                    B = 0;
                  (C && (T |= 16),
                    "UNIX" === o
                      ? ((B = 798),
                        (T |= (function (e, t) {
                          var r = e;
                          return (
                            e || (r = t ? 16893 : 33204),
                            (65535 & r) << 16
                          );
                        })(f.unixPermissions, C)))
                      : ((B = 20),
                        (T |= (function (e) {
                          return 63 & (e || 0);
                        })(f.dosPermissions))),
                    (u = E.getUTCHours()),
                    (u <<= 6),
                    (u |= E.getUTCMinutes()),
                    (u <<= 5),
                    (u |= E.getUTCSeconds() / 2),
                    (h = E.getUTCFullYear() - 1980),
                    (h <<= 4),
                    (h |= E.getUTCMonth() + 1),
                    (h <<= 5),
                    (h |= E.getUTCDate()),
                    A &&
                      ((k = n(1, 1) + n(l(p), 4) + y),
                      (x += "up" + n(k.length, 2) + k)),
                    _ &&
                      ((S = n(1, 1) + n(l(w), 4) + v),
                      (x += "uc" + n(S.length, 2) + S)));
                  var D = "";
                  return (
                    (D += "\n\0"),
                    (D += n(I, 2)),
                    (D += m.magic),
                    (D += n(u, 2)),
                    (D += n(h, 2)),
                    (D += n(O.crc32, 4)),
                    (D += n(O.compressedSize, 4)),
                    (D += n(O.uncompressedSize, 4)),
                    (D += n(p.length, 2)),
                    (D += n(x.length, 2)),
                    {
                      fileRecord: d.LOCAL_FILE_HEADER + D + p + x,
                      dirRecord:
                        d.CENTRAL_FILE_HEADER +
                        n(B, 2) +
                        D +
                        n(w.length, 2) +
                        "\0\0\0\0" +
                        n(T, 4) +
                        n(i, 4) +
                        p +
                        x +
                        w,
                    }
                  );
                }
                var s = e("../utils"),
                  o = e("../stream/GenericWorker"),
                  a = e("../utf8"),
                  l = e("../crc32"),
                  d = e("../signature");
                function c(e, t, r, n) {
                  (o.call(this, "ZipFileWorker"),
                    (this.bytesWritten = 0),
                    (this.zipComment = t),
                    (this.zipPlatform = r),
                    (this.encodeFileName = n),
                    (this.streamFiles = e),
                    (this.accumulate = !1),
                    (this.contentBuffer = []),
                    (this.dirRecords = []),
                    (this.currentSourceOffset = 0),
                    (this.entriesCount = 0),
                    (this.currentFile = null),
                    (this._sources = []));
                }
                (s.inherits(c, o),
                  (c.prototype.push = function (e) {
                    var t = e.meta.percent || 0,
                      r = this.entriesCount,
                      n = this._sources.length;
                    this.accumulate
                      ? this.contentBuffer.push(e)
                      : ((this.bytesWritten += e.data.length),
                        o.prototype.push.call(this, {
                          data: e.data,
                          meta: {
                            currentFile: this.currentFile,
                            percent: r ? (t + 100 * (r - n - 1)) / r : 100,
                          },
                        }));
                  }),
                  (c.prototype.openedSource = function (e) {
                    ((this.currentSourceOffset = this.bytesWritten),
                      (this.currentFile = e.file.name));
                    var t = this.streamFiles && !e.file.dir;
                    if (t) {
                      var r = i(
                        e,
                        t,
                        !1,
                        this.currentSourceOffset,
                        this.zipPlatform,
                        this.encodeFileName,
                      );
                      this.push({ data: r.fileRecord, meta: { percent: 0 } });
                    } else this.accumulate = !0;
                  }),
                  (c.prototype.closedSource = function (e) {
                    this.accumulate = !1;
                    var t = this.streamFiles && !e.file.dir,
                      r = i(
                        e,
                        t,
                        !0,
                        this.currentSourceOffset,
                        this.zipPlatform,
                        this.encodeFileName,
                      );
                    if ((this.dirRecords.push(r.dirRecord), t))
                      this.push({
                        data: (function (e) {
                          return (
                            d.DATA_DESCRIPTOR +
                            n(e.crc32, 4) +
                            n(e.compressedSize, 4) +
                            n(e.uncompressedSize, 4)
                          );
                        })(e),
                        meta: { percent: 100 },
                      });
                    else
                      for (
                        this.push({ data: r.fileRecord, meta: { percent: 0 } });
                        this.contentBuffer.length;

                      )
                        this.push(this.contentBuffer.shift());
                    this.currentFile = null;
                  }),
                  (c.prototype.flush = function () {
                    for (
                      var e = this.bytesWritten, t = 0;
                      t < this.dirRecords.length;
                      t++
                    )
                      this.push({
                        data: this.dirRecords[t],
                        meta: { percent: 100 },
                      });
                    var r = this.bytesWritten - e,
                      i = (function (e, t, r, i, o) {
                        var a = s.transformTo("string", o(i));
                        return (
                          d.CENTRAL_DIRECTORY_END +
                          "\0\0\0\0" +
                          n(e, 2) +
                          n(e, 2) +
                          n(t, 4) +
                          n(r, 4) +
                          n(a.length, 2) +
                          a
                        );
                      })(
                        this.dirRecords.length,
                        r,
                        e,
                        this.zipComment,
                        this.encodeFileName,
                      );
                    this.push({ data: i, meta: { percent: 100 } });
                  }),
                  (c.prototype.prepareNextSource = function () {
                    ((this.previous = this._sources.shift()),
                      this.openedSource(this.previous.streamInfo),
                      this.isPaused
                        ? this.previous.pause()
                        : this.previous.resume());
                  }),
                  (c.prototype.registerPrevious = function (e) {
                    this._sources.push(e);
                    var t = this;
                    return (
                      e.on("data", function (e) {
                        t.processChunk(e);
                      }),
                      e.on("end", function () {
                        (t.closedSource(t.previous.streamInfo),
                          t._sources.length ? t.prepareNextSource() : t.end());
                      }),
                      e.on("error", function (e) {
                        t.error(e);
                      }),
                      this
                    );
                  }),
                  (c.prototype.resume = function () {
                    return (
                      !!o.prototype.resume.call(this) &&
                      (!this.previous && this._sources.length
                        ? (this.prepareNextSource(), !0)
                        : this.previous ||
                            this._sources.length ||
                            this.generatedError
                          ? void 0
                          : (this.end(), !0))
                    );
                  }),
                  (c.prototype.error = function (e) {
                    var t = this._sources;
                    if (!o.prototype.error.call(this, e)) return !1;
                    for (var r = 0; r < t.length; r++)
                      try {
                        t[r].error(e);
                      } catch (e) {}
                    return !0;
                  }),
                  (c.prototype.lock = function () {
                    o.prototype.lock.call(this);
                    for (var e = this._sources, t = 0; t < e.length; t++)
                      e[t].lock();
                  }),
                  (t.exports = c));
              },
              {
                "../crc32": 4,
                "../signature": 23,
                "../stream/GenericWorker": 28,
                "../utf8": 31,
                "../utils": 32,
              },
            ],
            9: [
              function (e, t, r) {
                "use strict";
                var n = e("../compressions"),
                  i = e("./ZipFileWorker");
                r.generateWorker = function (e, t, r) {
                  var s = new i(t.streamFiles, r, t.platform, t.encodeFileName),
                    o = 0;
                  try {
                    (e.forEach(function (e, r) {
                      o++;
                      var i = (function (e, t) {
                          var r = e || t,
                            i = n[r];
                          if (!i)
                            throw new Error(
                              r + " is not a valid compression method !",
                            );
                          return i;
                        })(r.options.compression, t.compression),
                        a =
                          r.options.compressionOptions ||
                          t.compressionOptions ||
                          {},
                        l = r.dir,
                        d = r.date;
                      r._compressWorker(i, a)
                        .withStreamInfo("file", {
                          name: e,
                          dir: l,
                          date: d,
                          comment: r.comment || "",
                          unixPermissions: r.unixPermissions,
                          dosPermissions: r.dosPermissions,
                        })
                        .pipe(s);
                    }),
                      (s.entriesCount = o));
                  } catch (e) {
                    s.error(e);
                  }
                  return s;
                };
              },
              { "../compressions": 3, "./ZipFileWorker": 8 },
            ],
            10: [
              function (e, t, r) {
                "use strict";
                function n() {
                  if (!(this instanceof n)) return new n();
                  if (arguments.length)
                    throw new Error(
                      "The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  ((this.files = Object.create(null)),
                    (this.comment = null),
                    (this.root = ""),
                    (this.clone = function () {
                      var e = new n();
                      for (var t in this)
                        "function" != typeof this[t] && (e[t] = this[t]);
                      return e;
                    }));
                }
                (((n.prototype = e("./object")).loadAsync = e("./load")),
                  (n.support = e("./support")),
                  (n.defaults = e("./defaults")),
                  (n.version = "3.10.1"),
                  (n.loadAsync = function (e, t) {
                    return new n().loadAsync(e, t);
                  }),
                  (n.external = e("./external")),
                  (t.exports = n));
              },
              {
                "./defaults": 5,
                "./external": 6,
                "./load": 11,
                "./object": 15,
                "./support": 30,
              },
            ],
            11: [
              function (e, t, r) {
                "use strict";
                var n = e("./utils"),
                  i = e("./external"),
                  s = e("./utf8"),
                  o = e("./zipEntries"),
                  a = e("./stream/Crc32Probe"),
                  l = e("./nodejsUtils");
                function d(e) {
                  return new i.Promise(function (t, r) {
                    var n = e.decompressed.getContentWorker().pipe(new a());
                    n.on("error", function (e) {
                      r(e);
                    })
                      .on("end", function () {
                        n.streamInfo.crc32 !== e.decompressed.crc32
                          ? r(new Error("Corrupted zip : CRC32 mismatch"))
                          : t();
                      })
                      .resume();
                  });
                }
                t.exports = function (e, t) {
                  var r = this;
                  return (
                    (t = n.extend(t || {}, {
                      base64: !1,
                      checkCRC32: !1,
                      optimizedBinaryString: !1,
                      createFolders: !1,
                      decodeFileName: s.utf8decode,
                    })),
                    l.isNode && l.isStream(e)
                      ? i.Promise.reject(
                          new Error(
                            "JSZip can't accept a stream when loading a zip file.",
                          ),
                        )
                      : n
                          .prepareContent(
                            "the loaded zip file",
                            e,
                            !0,
                            t.optimizedBinaryString,
                            t.base64,
                          )
                          .then(function (e) {
                            var r = new o(t);
                            return (r.load(e), r);
                          })
                          .then(function (e) {
                            var r = [i.Promise.resolve(e)],
                              n = e.files;
                            if (t.checkCRC32)
                              for (var s = 0; s < n.length; s++)
                                r.push(d(n[s]));
                            return i.Promise.all(r);
                          })
                          .then(function (e) {
                            for (
                              var i = e.shift(), s = i.files, o = 0;
                              o < s.length;
                              o++
                            ) {
                              var a = s[o],
                                l = a.fileNameStr,
                                d = n.resolve(a.fileNameStr);
                              (r.file(d, a.decompressed, {
                                binary: !0,
                                optimizedBinaryString: !0,
                                date: a.date,
                                dir: a.dir,
                                comment: a.fileCommentStr.length
                                  ? a.fileCommentStr
                                  : null,
                                unixPermissions: a.unixPermissions,
                                dosPermissions: a.dosPermissions,
                                createFolders: t.createFolders,
                              }),
                                a.dir || (r.file(d).unsafeOriginalName = l));
                            }
                            return (
                              i.zipComment.length && (r.comment = i.zipComment),
                              r
                            );
                          })
                  );
                };
              },
              {
                "./external": 6,
                "./nodejsUtils": 14,
                "./stream/Crc32Probe": 25,
                "./utf8": 31,
                "./utils": 32,
                "./zipEntries": 33,
              },
            ],
            12: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("../stream/GenericWorker");
                function s(e, t) {
                  (i.call(this, "Nodejs stream input adapter for " + e),
                    (this._upstreamEnded = !1),
                    this._bindStream(t));
                }
                (n.inherits(s, i),
                  (s.prototype._bindStream = function (e) {
                    var t = this;
                    ((this._stream = e).pause(),
                      e
                        .on("data", function (e) {
                          t.push({ data: e, meta: { percent: 0 } });
                        })
                        .on("error", function (e) {
                          t.isPaused ? (this.generatedError = e) : t.error(e);
                        })
                        .on("end", function () {
                          t.isPaused ? (t._upstreamEnded = !0) : t.end();
                        }));
                  }),
                  (s.prototype.pause = function () {
                    return (
                      !!i.prototype.pause.call(this) &&
                      (this._stream.pause(), !0)
                    );
                  }),
                  (s.prototype.resume = function () {
                    return (
                      !!i.prototype.resume.call(this) &&
                      (this._upstreamEnded ? this.end() : this._stream.resume(),
                      !0)
                    );
                  }),
                  (t.exports = s));
              },
              { "../stream/GenericWorker": 28, "../utils": 32 },
            ],
            13: [
              function (e, t, r) {
                "use strict";
                var n = e("readable-stream").Readable;
                function i(e, t, r) {
                  (n.call(this, t), (this._helper = e));
                  var i = this;
                  e.on("data", function (e, t) {
                    (i.push(e) || i._helper.pause(), r && r(t));
                  })
                    .on("error", function (e) {
                      i.emit("error", e);
                    })
                    .on("end", function () {
                      i.push(null);
                    });
                }
                (e("../utils").inherits(i, n),
                  (i.prototype._read = function () {
                    this._helper.resume();
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "readable-stream": 16 },
            ],
            14: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  isNode: "undefined" != typeof Buffer,
                  newBufferFrom: function (e, t) {
                    if (Buffer.from && Buffer.from !== Uint8Array.from)
                      return Buffer.from(e, t);
                    if ("number" == typeof e)
                      throw new Error(
                        'The "data" argument must not be a number',
                      );
                    return new Buffer(e, t);
                  },
                  allocBuffer: function (e) {
                    if (Buffer.alloc) return Buffer.alloc(e);
                    var t = new Buffer(e);
                    return (t.fill(0), t);
                  },
                  isBuffer: function (e) {
                    return Buffer.isBuffer(e);
                  },
                  isStream: function (e) {
                    return (
                      e &&
                      "function" == typeof e.on &&
                      "function" == typeof e.pause &&
                      "function" == typeof e.resume
                    );
                  },
                };
              },
              {},
            ],
            15: [
              function (e, t, r) {
                "use strict";
                function n(e, t, r) {
                  var n,
                    i = s.getTypeOf(t),
                    a = s.extend(r || {}, l);
                  ((a.date = a.date || new Date()),
                    null !== a.compression &&
                      (a.compression = a.compression.toUpperCase()),
                    "string" == typeof a.unixPermissions &&
                      (a.unixPermissions = parseInt(a.unixPermissions, 8)),
                    a.unixPermissions &&
                      16384 & a.unixPermissions &&
                      (a.dir = !0),
                    a.dosPermissions && 16 & a.dosPermissions && (a.dir = !0),
                    a.dir && (e = g(e)),
                    a.createFolders && (n = m(e)) && p.call(this, n, !0));
                  var u = "string" === i && !1 === a.binary && !1 === a.base64;
                  ((r && void 0 !== r.binary) || (a.binary = !u),
                    ((t instanceof d && 0 === t.uncompressedSize) ||
                      a.dir ||
                      !t ||
                      0 === t.length) &&
                      ((a.base64 = !1),
                      (a.binary = !0),
                      (t = ""),
                      (a.compression = "STORE"),
                      (i = "string")));
                  var y = null;
                  y =
                    t instanceof d || t instanceof o
                      ? t
                      : h.isNode && h.isStream(t)
                        ? new f(e, t)
                        : s.prepareContent(
                            e,
                            t,
                            a.binary,
                            a.optimizedBinaryString,
                            a.base64,
                          );
                  var b = new c(e, y, a);
                  this.files[e] = b;
                }
                var i = e("./utf8"),
                  s = e("./utils"),
                  o = e("./stream/GenericWorker"),
                  a = e("./stream/StreamHelper"),
                  l = e("./defaults"),
                  d = e("./compressedObject"),
                  c = e("./zipObject"),
                  u = e("./generate"),
                  h = e("./nodejsUtils"),
                  f = e("./nodejs/NodejsStreamInputAdapter"),
                  m = function (e) {
                    "/" === e.slice(-1) && (e = e.substring(0, e.length - 1));
                    var t = e.lastIndexOf("/");
                    return 0 < t ? e.substring(0, t) : "";
                  },
                  g = function (e) {
                    return ("/" !== e.slice(-1) && (e += "/"), e);
                  },
                  p = function (e, t) {
                    return (
                      (t = void 0 !== t ? t : l.createFolders),
                      (e = g(e)),
                      this.files[e] ||
                        n.call(this, e, null, { dir: !0, createFolders: t }),
                      this.files[e]
                    );
                  };
                function y(e) {
                  return (
                    "[object RegExp]" === Object.prototype.toString.call(e)
                  );
                }
                var b = {
                  load: function () {
                    throw new Error(
                      "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  },
                  forEach: function (e) {
                    var t, r, n;
                    for (t in this.files)
                      ((n = this.files[t]),
                        (r = t.slice(this.root.length, t.length)) &&
                          t.slice(0, this.root.length) === this.root &&
                          e(r, n));
                  },
                  filter: function (e) {
                    var t = [];
                    return (
                      this.forEach(function (r, n) {
                        e(r, n) && t.push(n);
                      }),
                      t
                    );
                  },
                  file: function (e, t, r) {
                    if (1 !== arguments.length)
                      return ((e = this.root + e), n.call(this, e, t, r), this);
                    if (y(e)) {
                      var i = e;
                      return this.filter(function (e, t) {
                        return !t.dir && i.test(e);
                      });
                    }
                    var s = this.files[this.root + e];
                    return s && !s.dir ? s : null;
                  },
                  folder: function (e) {
                    if (!e) return this;
                    if (y(e))
                      return this.filter(function (t, r) {
                        return r.dir && e.test(t);
                      });
                    var t = this.root + e,
                      r = p.call(this, t),
                      n = this.clone();
                    return ((n.root = r.name), n);
                  },
                  remove: function (e) {
                    e = this.root + e;
                    var t = this.files[e];
                    if (
                      (t ||
                        ("/" !== e.slice(-1) && (e += "/"),
                        (t = this.files[e])),
                      t && !t.dir)
                    )
                      delete this.files[e];
                    else
                      for (
                        var r = this.filter(function (t, r) {
                            return r.name.slice(0, e.length) === e;
                          }),
                          n = 0;
                        n < r.length;
                        n++
                      )
                        delete this.files[r[n].name];
                    return this;
                  },
                  generate: function () {
                    throw new Error(
                      "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                    );
                  },
                  generateInternalStream: function (e) {
                    var t,
                      r = {};
                    try {
                      if (
                        (((r = s.extend(e || {}, {
                          streamFiles: !1,
                          compression: "STORE",
                          compressionOptions: null,
                          type: "",
                          platform: "DOS",
                          comment: null,
                          mimeType: "application/zip",
                          encodeFileName: i.utf8encode,
                        })).type = r.type.toLowerCase()),
                        (r.compression = r.compression.toUpperCase()),
                        "binarystring" === r.type && (r.type = "string"),
                        !r.type)
                      )
                        throw new Error("No output type specified.");
                      (s.checkSupport(r.type),
                        ("darwin" !== r.platform &&
                          "freebsd" !== r.platform &&
                          "linux" !== r.platform &&
                          "sunos" !== r.platform) ||
                          (r.platform = "UNIX"),
                        "win32" === r.platform && (r.platform = "DOS"));
                      var n = r.comment || this.comment || "";
                      t = u.generateWorker(this, r, n);
                    } catch (e) {
                      (t = new o("error")).error(e);
                    }
                    return new a(t, r.type || "string", r.mimeType);
                  },
                  generateAsync: function (e, t) {
                    return this.generateInternalStream(e).accumulate(t);
                  },
                  generateNodeStream: function (e, t) {
                    return (
                      (e = e || {}).type || (e.type = "nodebuffer"),
                      this.generateInternalStream(e).toNodejsStream(t)
                    );
                  },
                };
                t.exports = b;
              },
              {
                "./compressedObject": 2,
                "./defaults": 5,
                "./generate": 9,
                "./nodejs/NodejsStreamInputAdapter": 12,
                "./nodejsUtils": 14,
                "./stream/GenericWorker": 28,
                "./stream/StreamHelper": 29,
                "./utf8": 31,
                "./utils": 32,
                "./zipObject": 35,
              },
            ],
            16: [
              function (e, t, r) {
                "use strict";
                t.exports = e("stream");
              },
              { stream: void 0 },
            ],
            17: [
              function (e, t, r) {
                "use strict";
                var n = e("./DataReader");
                function i(e) {
                  n.call(this, e);
                  for (var t = 0; t < this.data.length; t++) e[t] = 255 & e[t];
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.byteAt = function (e) {
                    return this.data[this.zero + e];
                  }),
                  (i.prototype.lastIndexOfSignature = function (e) {
                    for (
                      var t = e.charCodeAt(0),
                        r = e.charCodeAt(1),
                        n = e.charCodeAt(2),
                        i = e.charCodeAt(3),
                        s = this.length - 4;
                      0 <= s;
                      --s
                    )
                      if (
                        this.data[s] === t &&
                        this.data[s + 1] === r &&
                        this.data[s + 2] === n &&
                        this.data[s + 3] === i
                      )
                        return s - this.zero;
                    return -1;
                  }),
                  (i.prototype.readAndCheckSignature = function (e) {
                    var t = e.charCodeAt(0),
                      r = e.charCodeAt(1),
                      n = e.charCodeAt(2),
                      i = e.charCodeAt(3),
                      s = this.readData(4);
                    return t === s[0] && r === s[1] && n === s[2] && i === s[3];
                  }),
                  (i.prototype.readData = function (e) {
                    if ((this.checkOffset(e), 0 === e)) return [];
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./DataReader": 18 },
            ],
            18: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils");
                function i(e) {
                  ((this.data = e),
                    (this.length = e.length),
                    (this.index = 0),
                    (this.zero = 0));
                }
                ((i.prototype = {
                  checkOffset: function (e) {
                    this.checkIndex(this.index + e);
                  },
                  checkIndex: function (e) {
                    if (this.length < this.zero + e || e < 0)
                      throw new Error(
                        "End of data reached (data length = " +
                          this.length +
                          ", asked index = " +
                          e +
                          "). Corrupted zip ?",
                      );
                  },
                  setIndex: function (e) {
                    (this.checkIndex(e), (this.index = e));
                  },
                  skip: function (e) {
                    this.setIndex(this.index + e);
                  },
                  byteAt: function () {},
                  readInt: function (e) {
                    var t,
                      r = 0;
                    for (
                      this.checkOffset(e), t = this.index + e - 1;
                      t >= this.index;
                      t--
                    )
                      r = (r << 8) + this.byteAt(t);
                    return ((this.index += e), r);
                  },
                  readString: function (e) {
                    return n.transformTo("string", this.readData(e));
                  },
                  readData: function () {},
                  lastIndexOfSignature: function () {},
                  readAndCheckSignature: function () {},
                  readDate: function () {
                    var e = this.readInt(4);
                    return new Date(
                      Date.UTC(
                        1980 + ((e >> 25) & 127),
                        ((e >> 21) & 15) - 1,
                        (e >> 16) & 31,
                        (e >> 11) & 31,
                        (e >> 5) & 63,
                        (31 & e) << 1,
                      ),
                    );
                  },
                }),
                  (t.exports = i));
              },
              { "../utils": 32 },
            ],
            19: [
              function (e, t, r) {
                "use strict";
                var n = e("./Uint8ArrayReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.readData = function (e) {
                    this.checkOffset(e);
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./Uint8ArrayReader": 21 },
            ],
            20: [
              function (e, t, r) {
                "use strict";
                var n = e("./DataReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.byteAt = function (e) {
                    return this.data.charCodeAt(this.zero + e);
                  }),
                  (i.prototype.lastIndexOfSignature = function (e) {
                    return this.data.lastIndexOf(e) - this.zero;
                  }),
                  (i.prototype.readAndCheckSignature = function (e) {
                    return e === this.readData(4);
                  }),
                  (i.prototype.readData = function (e) {
                    this.checkOffset(e);
                    var t = this.data.slice(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./DataReader": 18 },
            ],
            21: [
              function (e, t, r) {
                "use strict";
                var n = e("./ArrayReader");
                function i(e) {
                  n.call(this, e);
                }
                (e("../utils").inherits(i, n),
                  (i.prototype.readData = function (e) {
                    if ((this.checkOffset(e), 0 === e))
                      return new Uint8Array(0);
                    var t = this.data.subarray(
                      this.zero + this.index,
                      this.zero + this.index + e,
                    );
                    return ((this.index += e), t);
                  }),
                  (t.exports = i));
              },
              { "../utils": 32, "./ArrayReader": 17 },
            ],
            22: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("../support"),
                  s = e("./ArrayReader"),
                  o = e("./StringReader"),
                  a = e("./NodeBufferReader"),
                  l = e("./Uint8ArrayReader");
                t.exports = function (e) {
                  var t = n.getTypeOf(e);
                  return (
                    n.checkSupport(t),
                    "string" !== t || i.uint8array
                      ? "nodebuffer" === t
                        ? new a(e)
                        : i.uint8array
                          ? new l(n.transformTo("uint8array", e))
                          : new s(n.transformTo("array", e))
                      : new o(e)
                  );
                };
              },
              {
                "../support": 30,
                "../utils": 32,
                "./ArrayReader": 17,
                "./NodeBufferReader": 19,
                "./StringReader": 20,
                "./Uint8ArrayReader": 21,
              },
            ],
            23: [
              function (e, t, r) {
                "use strict";
                ((r.LOCAL_FILE_HEADER = "PK"),
                  (r.CENTRAL_FILE_HEADER = "PK"),
                  (r.CENTRAL_DIRECTORY_END = "PK"),
                  (r.ZIP64_CENTRAL_DIRECTORY_LOCATOR = "PK"),
                  (r.ZIP64_CENTRAL_DIRECTORY_END = "PK"),
                  (r.DATA_DESCRIPTOR = "PK\b"));
              },
              {},
            ],
            24: [
              function (e, t, r) {
                "use strict";
                var n = e("./GenericWorker"),
                  i = e("../utils");
                function s(e) {
                  (n.call(this, "ConvertWorker to " + e), (this.destType = e));
                }
                (i.inherits(s, n),
                  (s.prototype.processChunk = function (e) {
                    this.push({
                      data: i.transformTo(this.destType, e.data),
                      meta: e.meta,
                    });
                  }),
                  (t.exports = s));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            25: [
              function (e, t, r) {
                "use strict";
                var n = e("./GenericWorker"),
                  i = e("../crc32");
                function s() {
                  (n.call(this, "Crc32Probe"), this.withStreamInfo("crc32", 0));
                }
                (e("../utils").inherits(s, n),
                  (s.prototype.processChunk = function (e) {
                    ((this.streamInfo.crc32 = i(
                      e.data,
                      this.streamInfo.crc32 || 0,
                    )),
                      this.push(e));
                  }),
                  (t.exports = s));
              },
              { "../crc32": 4, "../utils": 32, "./GenericWorker": 28 },
            ],
            26: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./GenericWorker");
                function s(e) {
                  (i.call(this, "DataLengthProbe for " + e),
                    (this.propName = e),
                    this.withStreamInfo(e, 0));
                }
                (n.inherits(s, i),
                  (s.prototype.processChunk = function (e) {
                    if (e) {
                      var t = this.streamInfo[this.propName] || 0;
                      this.streamInfo[this.propName] = t + e.data.length;
                    }
                    i.prototype.processChunk.call(this, e);
                  }),
                  (t.exports = s));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            27: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./GenericWorker");
                function s(e) {
                  i.call(this, "DataWorker");
                  var t = this;
                  ((this.dataIsReady = !1),
                    (this.index = 0),
                    (this.max = 0),
                    (this.data = null),
                    (this.type = ""),
                    (this._tickScheduled = !1),
                    e.then(
                      function (e) {
                        ((t.dataIsReady = !0),
                          (t.data = e),
                          (t.max = (e && e.length) || 0),
                          (t.type = n.getTypeOf(e)),
                          t.isPaused || t._tickAndRepeat());
                      },
                      function (e) {
                        t.error(e);
                      },
                    ));
                }
                (n.inherits(s, i),
                  (s.prototype.cleanUp = function () {
                    (i.prototype.cleanUp.call(this), (this.data = null));
                  }),
                  (s.prototype.resume = function () {
                    return (
                      !!i.prototype.resume.call(this) &&
                      (!this._tickScheduled &&
                        this.dataIsReady &&
                        ((this._tickScheduled = !0),
                        n.delay(this._tickAndRepeat, [], this)),
                      !0)
                    );
                  }),
                  (s.prototype._tickAndRepeat = function () {
                    ((this._tickScheduled = !1),
                      this.isPaused ||
                        this.isFinished ||
                        (this._tick(),
                        this.isFinished ||
                          (n.delay(this._tickAndRepeat, [], this),
                          (this._tickScheduled = !0))));
                  }),
                  (s.prototype._tick = function () {
                    if (this.isPaused || this.isFinished) return !1;
                    var e = null,
                      t = Math.min(this.max, this.index + 16384);
                    if (this.index >= this.max) return this.end();
                    switch (this.type) {
                      case "string":
                        e = this.data.substring(this.index, t);
                        break;
                      case "uint8array":
                        e = this.data.subarray(this.index, t);
                        break;
                      case "array":
                      case "nodebuffer":
                        e = this.data.slice(this.index, t);
                    }
                    return (
                      (this.index = t),
                      this.push({
                        data: e,
                        meta: {
                          percent: this.max ? (this.index / this.max) * 100 : 0,
                        },
                      })
                    );
                  }),
                  (t.exports = s));
              },
              { "../utils": 32, "./GenericWorker": 28 },
            ],
            28: [
              function (e, t, r) {
                "use strict";
                function n(e) {
                  ((this.name = e || "default"),
                    (this.streamInfo = {}),
                    (this.generatedError = null),
                    (this.extraStreamInfo = {}),
                    (this.isPaused = !0),
                    (this.isFinished = !1),
                    (this.isLocked = !1),
                    (this._listeners = { data: [], end: [], error: [] }),
                    (this.previous = null));
                }
                ((n.prototype = {
                  push: function (e) {
                    this.emit("data", e);
                  },
                  end: function () {
                    if (this.isFinished) return !1;
                    this.flush();
                    try {
                      (this.emit("end"),
                        this.cleanUp(),
                        (this.isFinished = !0));
                    } catch (e) {
                      this.emit("error", e);
                    }
                    return !0;
                  },
                  error: function (e) {
                    return (
                      !this.isFinished &&
                      (this.isPaused
                        ? (this.generatedError = e)
                        : ((this.isFinished = !0),
                          this.emit("error", e),
                          this.previous && this.previous.error(e),
                          this.cleanUp()),
                      !0)
                    );
                  },
                  on: function (e, t) {
                    return (this._listeners[e].push(t), this);
                  },
                  cleanUp: function () {
                    ((this.streamInfo =
                      this.generatedError =
                      this.extraStreamInfo =
                        null),
                      (this._listeners = []));
                  },
                  emit: function (e, t) {
                    if (this._listeners[e])
                      for (var r = 0; r < this._listeners[e].length; r++)
                        this._listeners[e][r].call(this, t);
                  },
                  pipe: function (e) {
                    return e.registerPrevious(this);
                  },
                  registerPrevious: function (e) {
                    if (this.isLocked)
                      throw new Error(
                        "The stream '" + this + "' has already been used.",
                      );
                    ((this.streamInfo = e.streamInfo),
                      this.mergeStreamInfo(),
                      (this.previous = e));
                    var t = this;
                    return (
                      e.on("data", function (e) {
                        t.processChunk(e);
                      }),
                      e.on("end", function () {
                        t.end();
                      }),
                      e.on("error", function (e) {
                        t.error(e);
                      }),
                      this
                    );
                  },
                  pause: function () {
                    return (
                      !this.isPaused &&
                      !this.isFinished &&
                      ((this.isPaused = !0),
                      this.previous && this.previous.pause(),
                      !0)
                    );
                  },
                  resume: function () {
                    if (!this.isPaused || this.isFinished) return !1;
                    var e = (this.isPaused = !1);
                    return (
                      this.generatedError &&
                        (this.error(this.generatedError), (e = !0)),
                      this.previous && this.previous.resume(),
                      !e
                    );
                  },
                  flush: function () {},
                  processChunk: function (e) {
                    this.push(e);
                  },
                  withStreamInfo: function (e, t) {
                    return (
                      (this.extraStreamInfo[e] = t),
                      this.mergeStreamInfo(),
                      this
                    );
                  },
                  mergeStreamInfo: function () {
                    for (var e in this.extraStreamInfo)
                      Object.prototype.hasOwnProperty.call(
                        this.extraStreamInfo,
                        e,
                      ) && (this.streamInfo[e] = this.extraStreamInfo[e]);
                  },
                  lock: function () {
                    if (this.isLocked)
                      throw new Error(
                        "The stream '" + this + "' has already been used.",
                      );
                    ((this.isLocked = !0),
                      this.previous && this.previous.lock());
                  },
                  toString: function () {
                    var e = "Worker " + this.name;
                    return this.previous ? this.previous + " -> " + e : e;
                  },
                }),
                  (t.exports = n));
              },
              {},
            ],
            29: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils"),
                  i = e("./ConvertWorker"),
                  s = e("./GenericWorker"),
                  o = e("../base64"),
                  a = e("../support"),
                  l = e("../external"),
                  d = null;
                if (a.nodestream)
                  try {
                    d = e("../nodejs/NodejsStreamOutputAdapter");
                  } catch (e) {}
                function c(e, t) {
                  return new l.Promise(function (r, i) {
                    var s = [],
                      a = e._internalType,
                      l = e._outputType,
                      d = e._mimeType;
                    e.on("data", function (e, r) {
                      (s.push(e), t && t(r));
                    })
                      .on("error", function (e) {
                        ((s = []), i(e));
                      })
                      .on("end", function () {
                        try {
                          var e = (function (e, t, r) {
                            switch (e) {
                              case "blob":
                                return n.newBlob(
                                  n.transformTo("arraybuffer", t),
                                  r,
                                );
                              case "base64":
                                return o.encode(t);
                              default:
                                return n.transformTo(e, t);
                            }
                          })(
                            l,
                            (function (e, t) {
                              var r,
                                n = 0,
                                i = null,
                                s = 0;
                              for (r = 0; r < t.length; r++) s += t[r].length;
                              switch (e) {
                                case "string":
                                  return t.join("");
                                case "array":
                                  return Array.prototype.concat.apply([], t);
                                case "uint8array":
                                  for (
                                    i = new Uint8Array(s), r = 0;
                                    r < t.length;
                                    r++
                                  )
                                    (i.set(t[r], n), (n += t[r].length));
                                  return i;
                                case "nodebuffer":
                                  return Buffer.concat(t);
                                default:
                                  throw new Error(
                                    "concat : unsupported type '" + e + "'",
                                  );
                              }
                            })(a, s),
                            d,
                          );
                          r(e);
                        } catch (e) {
                          i(e);
                        }
                        s = [];
                      })
                      .resume();
                  });
                }
                function u(e, t, r) {
                  var o = t;
                  switch (t) {
                    case "blob":
                    case "arraybuffer":
                      o = "uint8array";
                      break;
                    case "base64":
                      o = "string";
                  }
                  try {
                    ((this._internalType = o),
                      (this._outputType = t),
                      (this._mimeType = r),
                      n.checkSupport(o),
                      (this._worker = e.pipe(new i(o))),
                      e.lock());
                  } catch (e) {
                    ((this._worker = new s("error")), this._worker.error(e));
                  }
                }
                ((u.prototype = {
                  accumulate: function (e) {
                    return c(this, e);
                  },
                  on: function (e, t) {
                    var r = this;
                    return (
                      "data" === e
                        ? this._worker.on(e, function (e) {
                            t.call(r, e.data, e.meta);
                          })
                        : this._worker.on(e, function () {
                            n.delay(t, arguments, r);
                          }),
                      this
                    );
                  },
                  resume: function () {
                    return (
                      n.delay(this._worker.resume, [], this._worker),
                      this
                    );
                  },
                  pause: function () {
                    return (this._worker.pause(), this);
                  },
                  toNodejsStream: function (e) {
                    if (
                      (n.checkSupport("nodestream"),
                      "nodebuffer" !== this._outputType)
                    )
                      throw new Error(
                        this._outputType + " is not supported by this method",
                      );
                    return new d(
                      this,
                      { objectMode: "nodebuffer" !== this._outputType },
                      e,
                    );
                  },
                }),
                  (t.exports = u));
              },
              {
                "../base64": 1,
                "../external": 6,
                "../nodejs/NodejsStreamOutputAdapter": 13,
                "../support": 30,
                "../utils": 32,
                "./ConvertWorker": 24,
                "./GenericWorker": 28,
              },
            ],
            30: [
              function (e, t, r) {
                "use strict";
                if (
                  ((r.base64 = !0),
                  (r.array = !0),
                  (r.string = !0),
                  (r.arraybuffer =
                    "undefined" != typeof ArrayBuffer &&
                    "undefined" != typeof Uint8Array),
                  (r.nodebuffer = "undefined" != typeof Buffer),
                  (r.uint8array = "undefined" != typeof Uint8Array),
                  "undefined" == typeof ArrayBuffer)
                )
                  r.blob = !1;
                else {
                  var n = new ArrayBuffer(0);
                  try {
                    r.blob =
                      0 === new Blob([n], { type: "application/zip" }).size;
                  } catch (e) {
                    try {
                      var i = new (self.BlobBuilder ||
                        self.WebKitBlobBuilder ||
                        self.MozBlobBuilder ||
                        self.MSBlobBuilder)();
                      (i.append(n),
                        (r.blob = 0 === i.getBlob("application/zip").size));
                    } catch (e) {
                      r.blob = !1;
                    }
                  }
                }
                try {
                  r.nodestream = !!e("readable-stream").Readable;
                } catch (e) {
                  r.nodestream = !1;
                }
              },
              { "readable-stream": 16 },
            ],
            31: [
              function (e, t, r) {
                "use strict";
                for (
                  var n = e("./utils"),
                    i = e("./support"),
                    s = e("./nodejsUtils"),
                    o = e("./stream/GenericWorker"),
                    a = new Array(256),
                    l = 0;
                  l < 256;
                  l++
                )
                  a[l] =
                    252 <= l
                      ? 6
                      : 248 <= l
                        ? 5
                        : 240 <= l
                          ? 4
                          : 224 <= l
                            ? 3
                            : 192 <= l
                              ? 2
                              : 1;
                function d() {
                  (o.call(this, "utf-8 decode"), (this.leftOver = null));
                }
                function c() {
                  o.call(this, "utf-8 encode");
                }
                ((a[254] = a[254] = 1),
                  (r.utf8encode = function (e) {
                    return i.nodebuffer
                      ? s.newBufferFrom(e, "utf-8")
                      : (function (e) {
                          var t,
                            r,
                            n,
                            s,
                            o,
                            a = e.length,
                            l = 0;
                          for (s = 0; s < a; s++)
                            (55296 == (64512 & (r = e.charCodeAt(s))) &&
                              s + 1 < a &&
                              56320 == (64512 & (n = e.charCodeAt(s + 1))) &&
                              ((r = 65536 + ((r - 55296) << 10) + (n - 56320)),
                              s++),
                              (l +=
                                r < 128
                                  ? 1
                                  : r < 2048
                                    ? 2
                                    : r < 65536
                                      ? 3
                                      : 4));
                          for (
                            t = i.uint8array ? new Uint8Array(l) : new Array(l),
                              s = o = 0;
                            o < l;
                            s++
                          )
                            (55296 == (64512 & (r = e.charCodeAt(s))) &&
                              s + 1 < a &&
                              56320 == (64512 & (n = e.charCodeAt(s + 1))) &&
                              ((r = 65536 + ((r - 55296) << 10) + (n - 56320)),
                              s++),
                              r < 128
                                ? (t[o++] = r)
                                : (r < 2048
                                    ? (t[o++] = 192 | (r >>> 6))
                                    : (r < 65536
                                        ? (t[o++] = 224 | (r >>> 12))
                                        : ((t[o++] = 240 | (r >>> 18)),
                                          (t[o++] = 128 | ((r >>> 12) & 63))),
                                      (t[o++] = 128 | ((r >>> 6) & 63))),
                                  (t[o++] = 128 | (63 & r))));
                          return t;
                        })(e);
                  }),
                  (r.utf8decode = function (e) {
                    return i.nodebuffer
                      ? n.transformTo("nodebuffer", e).toString("utf-8")
                      : (function (e) {
                          var t,
                            r,
                            i,
                            s,
                            o = e.length,
                            l = new Array(2 * o);
                          for (t = r = 0; t < o; )
                            if ((i = e[t++]) < 128) l[r++] = i;
                            else if (4 < (s = a[i]))
                              ((l[r++] = 65533), (t += s - 1));
                            else {
                              for (
                                i &= 2 === s ? 31 : 3 === s ? 15 : 7;
                                1 < s && t < o;

                              )
                                ((i = (i << 6) | (63 & e[t++])), s--);
                              1 < s
                                ? (l[r++] = 65533)
                                : i < 65536
                                  ? (l[r++] = i)
                                  : ((i -= 65536),
                                    (l[r++] = 55296 | ((i >> 10) & 1023)),
                                    (l[r++] = 56320 | (1023 & i)));
                            }
                          return (
                            l.length !== r &&
                              (l.subarray
                                ? (l = l.subarray(0, r))
                                : (l.length = r)),
                            n.applyFromCharCode(l)
                          );
                        })(
                          (e = n.transformTo(
                            i.uint8array ? "uint8array" : "array",
                            e,
                          )),
                        );
                  }),
                  n.inherits(d, o),
                  (d.prototype.processChunk = function (e) {
                    var t = n.transformTo(
                      i.uint8array ? "uint8array" : "array",
                      e.data,
                    );
                    if (this.leftOver && this.leftOver.length) {
                      if (i.uint8array) {
                        var s = t;
                        ((t = new Uint8Array(
                          s.length + this.leftOver.length,
                        )).set(this.leftOver, 0),
                          t.set(s, this.leftOver.length));
                      } else t = this.leftOver.concat(t);
                      this.leftOver = null;
                    }
                    var o = (function (e, t) {
                        var r;
                        for (
                          (t = t || e.length) > e.length && (t = e.length),
                            r = t - 1;
                          0 <= r && 128 == (192 & e[r]);

                        )
                          r--;
                        return r < 0 || 0 === r ? t : r + a[e[r]] > t ? r : t;
                      })(t),
                      l = t;
                    (o !== t.length &&
                      (i.uint8array
                        ? ((l = t.subarray(0, o)),
                          (this.leftOver = t.subarray(o, t.length)))
                        : ((l = t.slice(0, o)),
                          (this.leftOver = t.slice(o, t.length)))),
                      this.push({ data: r.utf8decode(l), meta: e.meta }));
                  }),
                  (d.prototype.flush = function () {
                    this.leftOver &&
                      this.leftOver.length &&
                      (this.push({
                        data: r.utf8decode(this.leftOver),
                        meta: {},
                      }),
                      (this.leftOver = null));
                  }),
                  (r.Utf8DecodeWorker = d),
                  n.inherits(c, o),
                  (c.prototype.processChunk = function (e) {
                    this.push({ data: r.utf8encode(e.data), meta: e.meta });
                  }),
                  (r.Utf8EncodeWorker = c));
              },
              {
                "./nodejsUtils": 14,
                "./stream/GenericWorker": 28,
                "./support": 30,
                "./utils": 32,
              },
            ],
            32: [
              function (e, t, r) {
                "use strict";
                var n = e("./support"),
                  i = e("./base64"),
                  s = e("./nodejsUtils"),
                  o = e("./external");
                function a(e) {
                  return e;
                }
                function l(e, t) {
                  for (var r = 0; r < e.length; ++r)
                    t[r] = 255 & e.charCodeAt(r);
                  return t;
                }
                (e("setimmediate"),
                  (r.newBlob = function (e, t) {
                    r.checkSupport("blob");
                    try {
                      return new Blob([e], { type: t });
                    } catch (r) {
                      try {
                        var n = new (self.BlobBuilder ||
                          self.WebKitBlobBuilder ||
                          self.MozBlobBuilder ||
                          self.MSBlobBuilder)();
                        return (n.append(e), n.getBlob(t));
                      } catch (e) {
                        throw new Error("Bug : can't construct the Blob.");
                      }
                    }
                  }));
                var d = {
                  stringifyByChunk: function (e, t, r) {
                    var n = [],
                      i = 0,
                      s = e.length;
                    if (s <= r) return String.fromCharCode.apply(null, e);
                    for (; i < s; )
                      ("array" === t || "nodebuffer" === t
                        ? n.push(
                            String.fromCharCode.apply(
                              null,
                              e.slice(i, Math.min(i + r, s)),
                            ),
                          )
                        : n.push(
                            String.fromCharCode.apply(
                              null,
                              e.subarray(i, Math.min(i + r, s)),
                            ),
                          ),
                        (i += r));
                    return n.join("");
                  },
                  stringifyByChar: function (e) {
                    for (var t = "", r = 0; r < e.length; r++)
                      t += String.fromCharCode(e[r]);
                    return t;
                  },
                  applyCanBeUsed: {
                    uint8array: (function () {
                      try {
                        return (
                          n.uint8array &&
                          1 ===
                            String.fromCharCode.apply(null, new Uint8Array(1))
                              .length
                        );
                      } catch (e) {
                        return !1;
                      }
                    })(),
                    nodebuffer: (function () {
                      try {
                        return (
                          n.nodebuffer &&
                          1 ===
                            String.fromCharCode.apply(null, s.allocBuffer(1))
                              .length
                        );
                      } catch (e) {
                        return !1;
                      }
                    })(),
                  },
                };
                function c(e) {
                  var t = 65536,
                    n = r.getTypeOf(e),
                    i = !0;
                  if (
                    ("uint8array" === n
                      ? (i = d.applyCanBeUsed.uint8array)
                      : "nodebuffer" === n && (i = d.applyCanBeUsed.nodebuffer),
                    i)
                  )
                    for (; 1 < t; )
                      try {
                        return d.stringifyByChunk(e, n, t);
                      } catch (e) {
                        t = Math.floor(t / 2);
                      }
                  return d.stringifyByChar(e);
                }
                function u(e, t) {
                  for (var r = 0; r < e.length; r++) t[r] = e[r];
                  return t;
                }
                r.applyFromCharCode = c;
                var h = {};
                ((h.string = {
                  string: a,
                  array: function (e) {
                    return l(e, new Array(e.length));
                  },
                  arraybuffer: function (e) {
                    return h.string.uint8array(e).buffer;
                  },
                  uint8array: function (e) {
                    return l(e, new Uint8Array(e.length));
                  },
                  nodebuffer: function (e) {
                    return l(e, s.allocBuffer(e.length));
                  },
                }),
                  (h.array = {
                    string: c,
                    array: a,
                    arraybuffer: function (e) {
                      return new Uint8Array(e).buffer;
                    },
                    uint8array: function (e) {
                      return new Uint8Array(e);
                    },
                    nodebuffer: function (e) {
                      return s.newBufferFrom(e);
                    },
                  }),
                  (h.arraybuffer = {
                    string: function (e) {
                      return c(new Uint8Array(e));
                    },
                    array: function (e) {
                      return u(new Uint8Array(e), new Array(e.byteLength));
                    },
                    arraybuffer: a,
                    uint8array: function (e) {
                      return new Uint8Array(e);
                    },
                    nodebuffer: function (e) {
                      return s.newBufferFrom(new Uint8Array(e));
                    },
                  }),
                  (h.uint8array = {
                    string: c,
                    array: function (e) {
                      return u(e, new Array(e.length));
                    },
                    arraybuffer: function (e) {
                      return e.buffer;
                    },
                    uint8array: a,
                    nodebuffer: function (e) {
                      return s.newBufferFrom(e);
                    },
                  }),
                  (h.nodebuffer = {
                    string: c,
                    array: function (e) {
                      return u(e, new Array(e.length));
                    },
                    arraybuffer: function (e) {
                      return h.nodebuffer.uint8array(e).buffer;
                    },
                    uint8array: function (e) {
                      return u(e, new Uint8Array(e.length));
                    },
                    nodebuffer: a,
                  }),
                  (r.transformTo = function (e, t) {
                    if (((t = t || ""), !e)) return t;
                    r.checkSupport(e);
                    var n = r.getTypeOf(t);
                    return h[n][e](t);
                  }),
                  (r.resolve = function (e) {
                    for (
                      var t = e.split("/"), r = [], n = 0;
                      n < t.length;
                      n++
                    ) {
                      var i = t[n];
                      "." === i ||
                        ("" === i && 0 !== n && n !== t.length - 1) ||
                        (".." === i ? r.pop() : r.push(i));
                    }
                    return r.join("/");
                  }),
                  (r.getTypeOf = function (e) {
                    return "string" == typeof e
                      ? "string"
                      : "[object Array]" === Object.prototype.toString.call(e)
                        ? "array"
                        : n.nodebuffer && s.isBuffer(e)
                          ? "nodebuffer"
                          : n.uint8array && e instanceof Uint8Array
                            ? "uint8array"
                            : n.arraybuffer && e instanceof ArrayBuffer
                              ? "arraybuffer"
                              : void 0;
                  }),
                  (r.checkSupport = function (e) {
                    if (!n[e.toLowerCase()])
                      throw new Error(e + " is not supported by this platform");
                  }),
                  (r.MAX_VALUE_16BITS = 65535),
                  (r.MAX_VALUE_32BITS = -1),
                  (r.pretty = function (e) {
                    var t,
                      r,
                      n = "";
                    for (r = 0; r < (e || "").length; r++)
                      n +=
                        "\\x" +
                        ((t = e.charCodeAt(r)) < 16 ? "0" : "") +
                        t.toString(16).toUpperCase();
                    return n;
                  }),
                  (r.delay = function (e, t, r) {
                    setImmediate(function () {
                      e.apply(r || null, t || []);
                    });
                  }),
                  (r.inherits = function (e, t) {
                    function r() {}
                    ((r.prototype = t.prototype), (e.prototype = new r()));
                  }),
                  (r.extend = function () {
                    var e,
                      t,
                      r = {};
                    for (e = 0; e < arguments.length; e++)
                      for (t in arguments[e])
                        Object.prototype.hasOwnProperty.call(arguments[e], t) &&
                          void 0 === r[t] &&
                          (r[t] = arguments[e][t]);
                    return r;
                  }),
                  (r.prepareContent = function (e, t, s, a, d) {
                    return o.Promise.resolve(t)
                      .then(function (e) {
                        return n.blob &&
                          (e instanceof Blob ||
                            -1 !==
                              ["[object File]", "[object Blob]"].indexOf(
                                Object.prototype.toString.call(e),
                              )) &&
                          "undefined" != typeof FileReader
                          ? new o.Promise(function (t, r) {
                              var n = new FileReader();
                              ((n.onload = function (e) {
                                t(e.target.result);
                              }),
                                (n.onerror = function (e) {
                                  r(e.target.error);
                                }),
                                n.readAsArrayBuffer(e));
                            })
                          : e;
                      })
                      .then(function (t) {
                        var c = r.getTypeOf(t);
                        return c
                          ? ("arraybuffer" === c
                              ? (t = r.transformTo("uint8array", t))
                              : "string" === c &&
                                (d
                                  ? (t = i.decode(t))
                                  : s &&
                                    !0 !== a &&
                                    (t = (function (e) {
                                      return l(
                                        e,
                                        n.uint8array
                                          ? new Uint8Array(e.length)
                                          : new Array(e.length),
                                      );
                                    })(t))),
                            t)
                          : o.Promise.reject(
                              new Error(
                                "Can't read the data of '" +
                                  e +
                                  "'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?",
                              ),
                            );
                      });
                  }));
              },
              {
                "./base64": 1,
                "./external": 6,
                "./nodejsUtils": 14,
                "./support": 30,
                setimmediate: 54,
              },
            ],
            33: [
              function (e, t, r) {
                "use strict";
                var n = e("./reader/readerFor"),
                  i = e("./utils"),
                  s = e("./signature"),
                  o = e("./zipEntry"),
                  a = e("./support");
                function l(e) {
                  ((this.files = []), (this.loadOptions = e));
                }
                ((l.prototype = {
                  checkSignature: function (e) {
                    if (!this.reader.readAndCheckSignature(e)) {
                      this.reader.index -= 4;
                      var t = this.reader.readString(4);
                      throw new Error(
                        "Corrupted zip or bug: unexpected signature (" +
                          i.pretty(t) +
                          ", expected " +
                          i.pretty(e) +
                          ")",
                      );
                    }
                  },
                  isSignature: function (e, t) {
                    var r = this.reader.index;
                    this.reader.setIndex(e);
                    var n = this.reader.readString(4) === t;
                    return (this.reader.setIndex(r), n);
                  },
                  readBlockEndOfCentral: function () {
                    ((this.diskNumber = this.reader.readInt(2)),
                      (this.diskWithCentralDirStart = this.reader.readInt(2)),
                      (this.centralDirRecordsOnThisDisk =
                        this.reader.readInt(2)),
                      (this.centralDirRecords = this.reader.readInt(2)),
                      (this.centralDirSize = this.reader.readInt(4)),
                      (this.centralDirOffset = this.reader.readInt(4)),
                      (this.zipCommentLength = this.reader.readInt(2)));
                    var e = this.reader.readData(this.zipCommentLength),
                      t = a.uint8array ? "uint8array" : "array",
                      r = i.transformTo(t, e);
                    this.zipComment = this.loadOptions.decodeFileName(r);
                  },
                  readBlockZip64EndOfCentral: function () {
                    ((this.zip64EndOfCentralSize = this.reader.readInt(8)),
                      this.reader.skip(4),
                      (this.diskNumber = this.reader.readInt(4)),
                      (this.diskWithCentralDirStart = this.reader.readInt(4)),
                      (this.centralDirRecordsOnThisDisk =
                        this.reader.readInt(8)),
                      (this.centralDirRecords = this.reader.readInt(8)),
                      (this.centralDirSize = this.reader.readInt(8)),
                      (this.centralDirOffset = this.reader.readInt(8)),
                      (this.zip64ExtensibleData = {}));
                    for (
                      var e, t, r, n = this.zip64EndOfCentralSize - 44;
                      0 < n;

                    )
                      ((e = this.reader.readInt(2)),
                        (t = this.reader.readInt(4)),
                        (r = this.reader.readData(t)),
                        (this.zip64ExtensibleData[e] = {
                          id: e,
                          length: t,
                          value: r,
                        }));
                  },
                  readBlockZip64EndOfCentralLocator: function () {
                    if (
                      ((this.diskWithZip64CentralDirStart =
                        this.reader.readInt(4)),
                      (this.relativeOffsetEndOfZip64CentralDir =
                        this.reader.readInt(8)),
                      (this.disksCount = this.reader.readInt(4)),
                      1 < this.disksCount)
                    )
                      throw new Error("Multi-volumes zip are not supported");
                  },
                  readLocalFiles: function () {
                    var e, t;
                    for (e = 0; e < this.files.length; e++)
                      ((t = this.files[e]),
                        this.reader.setIndex(t.localHeaderOffset),
                        this.checkSignature(s.LOCAL_FILE_HEADER),
                        t.readLocalPart(this.reader),
                        t.handleUTF8(),
                        t.processAttributes());
                  },
                  readCentralDir: function () {
                    var e;
                    for (
                      this.reader.setIndex(this.centralDirOffset);
                      this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);

                    )
                      ((e = new o(
                        { zip64: this.zip64 },
                        this.loadOptions,
                      )).readCentralPart(this.reader),
                        this.files.push(e));
                    if (
                      this.centralDirRecords !== this.files.length &&
                      0 !== this.centralDirRecords &&
                      0 === this.files.length
                    )
                      throw new Error(
                        "Corrupted zip or bug: expected " +
                          this.centralDirRecords +
                          " records in central dir, got " +
                          this.files.length,
                      );
                  },
                  readEndOfCentral: function () {
                    var e = this.reader.lastIndexOfSignature(
                      s.CENTRAL_DIRECTORY_END,
                    );
                    if (e < 0)
                      throw this.isSignature(0, s.LOCAL_FILE_HEADER)
                        ? new Error(
                            "Corrupted zip: can't find end of central directory",
                          )
                        : new Error(
                            "Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html",
                          );
                    this.reader.setIndex(e);
                    var t = e;
                    if (
                      (this.checkSignature(s.CENTRAL_DIRECTORY_END),
                      this.readBlockEndOfCentral(),
                      this.diskNumber === i.MAX_VALUE_16BITS ||
                        this.diskWithCentralDirStart === i.MAX_VALUE_16BITS ||
                        this.centralDirRecordsOnThisDisk ===
                          i.MAX_VALUE_16BITS ||
                        this.centralDirRecords === i.MAX_VALUE_16BITS ||
                        this.centralDirSize === i.MAX_VALUE_32BITS ||
                        this.centralDirOffset === i.MAX_VALUE_32BITS)
                    ) {
                      if (
                        ((this.zip64 = !0),
                        (e = this.reader.lastIndexOfSignature(
                          s.ZIP64_CENTRAL_DIRECTORY_LOCATOR,
                        )) < 0)
                      )
                        throw new Error(
                          "Corrupted zip: can't find the ZIP64 end of central directory locator",
                        );
                      if (
                        (this.reader.setIndex(e),
                        this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),
                        this.readBlockZip64EndOfCentralLocator(),
                        !this.isSignature(
                          this.relativeOffsetEndOfZip64CentralDir,
                          s.ZIP64_CENTRAL_DIRECTORY_END,
                        ) &&
                          ((this.relativeOffsetEndOfZip64CentralDir =
                            this.reader.lastIndexOfSignature(
                              s.ZIP64_CENTRAL_DIRECTORY_END,
                            )),
                          this.relativeOffsetEndOfZip64CentralDir < 0))
                      )
                        throw new Error(
                          "Corrupted zip: can't find the ZIP64 end of central directory",
                        );
                      (this.reader.setIndex(
                        this.relativeOffsetEndOfZip64CentralDir,
                      ),
                        this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),
                        this.readBlockZip64EndOfCentral());
                    }
                    var r = this.centralDirOffset + this.centralDirSize;
                    this.zip64 &&
                      ((r += 20), (r += 12 + this.zip64EndOfCentralSize));
                    var n = t - r;
                    if (0 < n)
                      this.isSignature(t, s.CENTRAL_FILE_HEADER) ||
                        (this.reader.zero = n);
                    else if (n < 0)
                      throw new Error(
                        "Corrupted zip: missing " + Math.abs(n) + " bytes.",
                      );
                  },
                  prepareReader: function (e) {
                    this.reader = n(e);
                  },
                  load: function (e) {
                    (this.prepareReader(e),
                      this.readEndOfCentral(),
                      this.readCentralDir(),
                      this.readLocalFiles());
                  },
                }),
                  (t.exports = l));
              },
              {
                "./reader/readerFor": 22,
                "./signature": 23,
                "./support": 30,
                "./utils": 32,
                "./zipEntry": 34,
              },
            ],
            34: [
              function (e, t, r) {
                "use strict";
                var n = e("./reader/readerFor"),
                  i = e("./utils"),
                  s = e("./compressedObject"),
                  o = e("./crc32"),
                  a = e("./utf8"),
                  l = e("./compressions"),
                  d = e("./support");
                function c(e, t) {
                  ((this.options = e), (this.loadOptions = t));
                }
                ((c.prototype = {
                  isEncrypted: function () {
                    return !(1 & ~this.bitFlag);
                  },
                  useUTF8: function () {
                    return !(2048 & ~this.bitFlag);
                  },
                  readLocalPart: function (e) {
                    var t, r;
                    if (
                      (e.skip(22),
                      (this.fileNameLength = e.readInt(2)),
                      (r = e.readInt(2)),
                      (this.fileName = e.readData(this.fileNameLength)),
                      e.skip(r),
                      -1 === this.compressedSize ||
                        -1 === this.uncompressedSize)
                    )
                      throw new Error(
                        "Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)",
                      );
                    if (
                      null ===
                      (t = (function (e) {
                        for (var t in l)
                          if (
                            Object.prototype.hasOwnProperty.call(l, t) &&
                            l[t].magic === e
                          )
                            return l[t];
                        return null;
                      })(this.compressionMethod))
                    )
                      throw new Error(
                        "Corrupted zip : compression " +
                          i.pretty(this.compressionMethod) +
                          " unknown (inner file : " +
                          i.transformTo("string", this.fileName) +
                          ")",
                      );
                    this.decompressed = new s(
                      this.compressedSize,
                      this.uncompressedSize,
                      this.crc32,
                      t,
                      e.readData(this.compressedSize),
                    );
                  },
                  readCentralPart: function (e) {
                    ((this.versionMadeBy = e.readInt(2)),
                      e.skip(2),
                      (this.bitFlag = e.readInt(2)),
                      (this.compressionMethod = e.readString(2)),
                      (this.date = e.readDate()),
                      (this.crc32 = e.readInt(4)),
                      (this.compressedSize = e.readInt(4)),
                      (this.uncompressedSize = e.readInt(4)));
                    var t = e.readInt(2);
                    if (
                      ((this.extraFieldsLength = e.readInt(2)),
                      (this.fileCommentLength = e.readInt(2)),
                      (this.diskNumberStart = e.readInt(2)),
                      (this.internalFileAttributes = e.readInt(2)),
                      (this.externalFileAttributes = e.readInt(4)),
                      (this.localHeaderOffset = e.readInt(4)),
                      this.isEncrypted())
                    )
                      throw new Error("Encrypted zip are not supported");
                    (e.skip(t),
                      this.readExtraFields(e),
                      this.parseZIP64ExtraField(e),
                      (this.fileComment = e.readData(this.fileCommentLength)));
                  },
                  processAttributes: function () {
                    ((this.unixPermissions = null),
                      (this.dosPermissions = null));
                    var e = this.versionMadeBy >> 8;
                    ((this.dir = !!(16 & this.externalFileAttributes)),
                      0 == e &&
                        (this.dosPermissions =
                          63 & this.externalFileAttributes),
                      3 == e &&
                        (this.unixPermissions =
                          (this.externalFileAttributes >> 16) & 65535),
                      this.dir ||
                        "/" !== this.fileNameStr.slice(-1) ||
                        (this.dir = !0));
                  },
                  parseZIP64ExtraField: function () {
                    if (this.extraFields[1]) {
                      var e = n(this.extraFields[1].value);
                      (this.uncompressedSize === i.MAX_VALUE_32BITS &&
                        (this.uncompressedSize = e.readInt(8)),
                        this.compressedSize === i.MAX_VALUE_32BITS &&
                          (this.compressedSize = e.readInt(8)),
                        this.localHeaderOffset === i.MAX_VALUE_32BITS &&
                          (this.localHeaderOffset = e.readInt(8)),
                        this.diskNumberStart === i.MAX_VALUE_32BITS &&
                          (this.diskNumberStart = e.readInt(4)));
                    }
                  },
                  readExtraFields: function (e) {
                    var t,
                      r,
                      n,
                      i = e.index + this.extraFieldsLength;
                    for (
                      this.extraFields || (this.extraFields = {});
                      e.index + 4 < i;

                    )
                      ((t = e.readInt(2)),
                        (r = e.readInt(2)),
                        (n = e.readData(r)),
                        (this.extraFields[t] = { id: t, length: r, value: n }));
                    e.setIndex(i);
                  },
                  handleUTF8: function () {
                    var e = d.uint8array ? "uint8array" : "array";
                    if (this.useUTF8())
                      ((this.fileNameStr = a.utf8decode(this.fileName)),
                        (this.fileCommentStr = a.utf8decode(this.fileComment)));
                    else {
                      var t = this.findExtraFieldUnicodePath();
                      if (null !== t) this.fileNameStr = t;
                      else {
                        var r = i.transformTo(e, this.fileName);
                        this.fileNameStr = this.loadOptions.decodeFileName(r);
                      }
                      var n = this.findExtraFieldUnicodeComment();
                      if (null !== n) this.fileCommentStr = n;
                      else {
                        var s = i.transformTo(e, this.fileComment);
                        this.fileCommentStr =
                          this.loadOptions.decodeFileName(s);
                      }
                    }
                  },
                  findExtraFieldUnicodePath: function () {
                    var e = this.extraFields[28789];
                    if (e) {
                      var t = n(e.value);
                      return 1 !== t.readInt(1) ||
                        o(this.fileName) !== t.readInt(4)
                        ? null
                        : a.utf8decode(t.readData(e.length - 5));
                    }
                    return null;
                  },
                  findExtraFieldUnicodeComment: function () {
                    var e = this.extraFields[25461];
                    if (e) {
                      var t = n(e.value);
                      return 1 !== t.readInt(1) ||
                        o(this.fileComment) !== t.readInt(4)
                        ? null
                        : a.utf8decode(t.readData(e.length - 5));
                    }
                    return null;
                  },
                }),
                  (t.exports = c));
              },
              {
                "./compressedObject": 2,
                "./compressions": 3,
                "./crc32": 4,
                "./reader/readerFor": 22,
                "./support": 30,
                "./utf8": 31,
                "./utils": 32,
              },
            ],
            35: [
              function (e, t, r) {
                "use strict";
                function n(e, t, r) {
                  ((this.name = e),
                    (this.dir = r.dir),
                    (this.date = r.date),
                    (this.comment = r.comment),
                    (this.unixPermissions = r.unixPermissions),
                    (this.dosPermissions = r.dosPermissions),
                    (this._data = t),
                    (this._dataBinary = r.binary),
                    (this.options = {
                      compression: r.compression,
                      compressionOptions: r.compressionOptions,
                    }));
                }
                var i = e("./stream/StreamHelper"),
                  s = e("./stream/DataWorker"),
                  o = e("./utf8"),
                  a = e("./compressedObject"),
                  l = e("./stream/GenericWorker");
                n.prototype = {
                  internalStream: function (e) {
                    var t = null,
                      r = "string";
                    try {
                      if (!e) throw new Error("No output type specified.");
                      var n =
                        "string" === (r = e.toLowerCase()) || "text" === r;
                      (("binarystring" !== r && "text" !== r) || (r = "string"),
                        (t = this._decompressWorker()));
                      var s = !this._dataBinary;
                      (s && !n && (t = t.pipe(new o.Utf8EncodeWorker())),
                        !s && n && (t = t.pipe(new o.Utf8DecodeWorker())));
                    } catch (e) {
                      (t = new l("error")).error(e);
                    }
                    return new i(t, r, "");
                  },
                  async: function (e, t) {
                    return this.internalStream(e).accumulate(t);
                  },
                  nodeStream: function (e, t) {
                    return this.internalStream(
                      e || "nodebuffer",
                    ).toNodejsStream(t);
                  },
                  _compressWorker: function (e, t) {
                    if (
                      this._data instanceof a &&
                      this._data.compression.magic === e.magic
                    )
                      return this._data.getCompressedWorker();
                    var r = this._decompressWorker();
                    return (
                      this._dataBinary ||
                        (r = r.pipe(new o.Utf8EncodeWorker())),
                      a.createWorkerFrom(r, e, t)
                    );
                  },
                  _decompressWorker: function () {
                    return this._data instanceof a
                      ? this._data.getContentWorker()
                      : this._data instanceof l
                        ? this._data
                        : new s(this._data);
                  },
                };
                for (
                  var d = [
                      "asText",
                      "asBinary",
                      "asNodeBuffer",
                      "asUint8Array",
                      "asArrayBuffer",
                    ],
                    c = function () {
                      throw new Error(
                        "This method has been removed in JSZip 3.0, please check the upgrade guide.",
                      );
                    },
                    u = 0;
                  u < d.length;
                  u++
                )
                  n.prototype[d[u]] = c;
                t.exports = n;
              },
              {
                "./compressedObject": 2,
                "./stream/DataWorker": 27,
                "./stream/GenericWorker": 28,
                "./stream/StreamHelper": 29,
                "./utf8": 31,
              },
            ],
            36: [
              function (e, t, r) {
                (function (e) {
                  "use strict";
                  var r,
                    n,
                    i = e.MutationObserver || e.WebKitMutationObserver;
                  if (i) {
                    var s = 0,
                      o = new i(c),
                      a = e.document.createTextNode("");
                    (o.observe(a, { characterData: !0 }),
                      (r = function () {
                        a.data = s = ++s % 2;
                      }));
                  } else if (e.setImmediate || void 0 === e.MessageChannel)
                    r =
                      "document" in e &&
                      "onreadystatechange" in e.document.createElement("script")
                        ? function () {
                            var t = e.document.createElement("script");
                            ((t.onreadystatechange = function () {
                              (c(),
                                (t.onreadystatechange = null),
                                t.parentNode.removeChild(t),
                                (t = null));
                            }),
                              e.document.documentElement.appendChild(t));
                          }
                        : function () {
                            setTimeout(c, 0);
                          };
                  else {
                    var l = new e.MessageChannel();
                    ((l.port1.onmessage = c),
                      (r = function () {
                        l.port2.postMessage(0);
                      }));
                  }
                  var d = [];
                  function c() {
                    var e, t;
                    n = !0;
                    for (var r = d.length; r; ) {
                      for (t = d, d = [], e = -1; ++e < r; ) t[e]();
                      r = d.length;
                    }
                    n = !1;
                  }
                  t.exports = function (e) {
                    1 !== d.push(e) || n || r();
                  };
                }).call(
                  this,
                  "undefined" != typeof global
                    ? global
                    : "undefined" != typeof self
                      ? self
                      : "undefined" != typeof window
                        ? window
                        : {},
                );
              },
              {},
            ],
            37: [
              function (e, t, r) {
                "use strict";
                var n = e("immediate");
                function i() {}
                var s = {},
                  o = ["REJECTED"],
                  a = ["FULFILLED"],
                  l = ["PENDING"];
                function d(e) {
                  if ("function" != typeof e)
                    throw new TypeError("resolver must be a function");
                  ((this.state = l),
                    (this.queue = []),
                    (this.outcome = void 0),
                    e !== i && f(this, e));
                }
                function c(e, t, r) {
                  ((this.promise = e),
                    "function" == typeof t &&
                      ((this.onFulfilled = t),
                      (this.callFulfilled = this.otherCallFulfilled)),
                    "function" == typeof r &&
                      ((this.onRejected = r),
                      (this.callRejected = this.otherCallRejected)));
                }
                function u(e, t, r) {
                  n(function () {
                    var n;
                    try {
                      n = t(r);
                    } catch (n) {
                      return s.reject(e, n);
                    }
                    n === e
                      ? s.reject(
                          e,
                          new TypeError("Cannot resolve promise with itself"),
                        )
                      : s.resolve(e, n);
                  });
                }
                function h(e) {
                  var t = e && e.then;
                  if (
                    e &&
                    ("object" == typeof e || "function" == typeof e) &&
                    "function" == typeof t
                  )
                    return function () {
                      t.apply(e, arguments);
                    };
                }
                function f(e, t) {
                  var r = !1;
                  function n(t) {
                    r || ((r = !0), s.reject(e, t));
                  }
                  function i(t) {
                    r || ((r = !0), s.resolve(e, t));
                  }
                  var o = m(function () {
                    t(i, n);
                  });
                  "error" === o.status && n(o.value);
                }
                function m(e, t) {
                  var r = {};
                  try {
                    ((r.value = e(t)), (r.status = "success"));
                  } catch (e) {
                    ((r.status = "error"), (r.value = e));
                  }
                  return r;
                }
                (((t.exports = d).prototype.finally = function (e) {
                  if ("function" != typeof e) return this;
                  var t = this.constructor;
                  return this.then(
                    function (r) {
                      return t.resolve(e()).then(function () {
                        return r;
                      });
                    },
                    function (r) {
                      return t.resolve(e()).then(function () {
                        throw r;
                      });
                    },
                  );
                }),
                  (d.prototype.catch = function (e) {
                    return this.then(null, e);
                  }),
                  (d.prototype.then = function (e, t) {
                    if (
                      ("function" != typeof e && this.state === a) ||
                      ("function" != typeof t && this.state === o)
                    )
                      return this;
                    var r = new this.constructor(i);
                    return (
                      this.state !== l
                        ? u(r, this.state === a ? e : t, this.outcome)
                        : this.queue.push(new c(r, e, t)),
                      r
                    );
                  }),
                  (c.prototype.callFulfilled = function (e) {
                    s.resolve(this.promise, e);
                  }),
                  (c.prototype.otherCallFulfilled = function (e) {
                    u(this.promise, this.onFulfilled, e);
                  }),
                  (c.prototype.callRejected = function (e) {
                    s.reject(this.promise, e);
                  }),
                  (c.prototype.otherCallRejected = function (e) {
                    u(this.promise, this.onRejected, e);
                  }),
                  (s.resolve = function (e, t) {
                    var r = m(h, t);
                    if ("error" === r.status) return s.reject(e, r.value);
                    var n = r.value;
                    if (n) f(e, n);
                    else {
                      ((e.state = a), (e.outcome = t));
                      for (var i = -1, o = e.queue.length; ++i < o; )
                        e.queue[i].callFulfilled(t);
                    }
                    return e;
                  }),
                  (s.reject = function (e, t) {
                    ((e.state = o), (e.outcome = t));
                    for (var r = -1, n = e.queue.length; ++r < n; )
                      e.queue[r].callRejected(t);
                    return e;
                  }),
                  (d.resolve = function (e) {
                    return e instanceof this ? e : s.resolve(new this(i), e);
                  }),
                  (d.reject = function (e) {
                    var t = new this(i);
                    return s.reject(t, e);
                  }),
                  (d.all = function (e) {
                    var t = this;
                    if ("[object Array]" !== Object.prototype.toString.call(e))
                      return this.reject(new TypeError("must be an array"));
                    var r = e.length,
                      n = !1;
                    if (!r) return this.resolve([]);
                    for (
                      var o = new Array(r), a = 0, l = -1, d = new this(i);
                      ++l < r;

                    )
                      c(e[l], l);
                    return d;
                    function c(e, i) {
                      t.resolve(e).then(
                        function (e) {
                          ((o[i] = e),
                            ++a !== r || n || ((n = !0), s.resolve(d, o)));
                        },
                        function (e) {
                          n || ((n = !0), s.reject(d, e));
                        },
                      );
                    }
                  }),
                  (d.race = function (e) {
                    var t = this;
                    if ("[object Array]" !== Object.prototype.toString.call(e))
                      return this.reject(new TypeError("must be an array"));
                    var r = e.length,
                      n = !1;
                    if (!r) return this.resolve([]);
                    for (var o, a = -1, l = new this(i); ++a < r; )
                      ((o = e[a]),
                        t.resolve(o).then(
                          function (e) {
                            n || ((n = !0), s.resolve(l, e));
                          },
                          function (e) {
                            n || ((n = !0), s.reject(l, e));
                          },
                        ));
                    return l;
                  }));
              },
              { immediate: 36 },
            ],
            38: [
              function (e, t, r) {
                "use strict";
                var n = {};
                ((0, e("./lib/utils/common").assign)(
                  n,
                  e("./lib/deflate"),
                  e("./lib/inflate"),
                  e("./lib/zlib/constants"),
                ),
                  (t.exports = n));
              },
              {
                "./lib/deflate": 39,
                "./lib/inflate": 40,
                "./lib/utils/common": 41,
                "./lib/zlib/constants": 44,
              },
            ],
            39: [
              function (e, t, r) {
                "use strict";
                var n = e("./zlib/deflate"),
                  i = e("./utils/common"),
                  s = e("./utils/strings"),
                  o = e("./zlib/messages"),
                  a = e("./zlib/zstream"),
                  l = Object.prototype.toString,
                  d = 0,
                  c = -1,
                  u = 0,
                  h = 8;
                function f(e) {
                  if (!(this instanceof f)) return new f(e);
                  this.options = i.assign(
                    {
                      level: c,
                      method: h,
                      chunkSize: 16384,
                      windowBits: 15,
                      memLevel: 8,
                      strategy: u,
                      to: "",
                    },
                    e || {},
                  );
                  var t = this.options;
                  (t.raw && 0 < t.windowBits
                    ? (t.windowBits = -t.windowBits)
                    : t.gzip &&
                      0 < t.windowBits &&
                      t.windowBits < 16 &&
                      (t.windowBits += 16),
                    (this.err = 0),
                    (this.msg = ""),
                    (this.ended = !1),
                    (this.chunks = []),
                    (this.strm = new a()),
                    (this.strm.avail_out = 0));
                  var r = n.deflateInit2(
                    this.strm,
                    t.level,
                    t.method,
                    t.windowBits,
                    t.memLevel,
                    t.strategy,
                  );
                  if (r !== d) throw new Error(o[r]);
                  if (
                    (t.header && n.deflateSetHeader(this.strm, t.header),
                    t.dictionary)
                  ) {
                    var m;
                    if (
                      ((m =
                        "string" == typeof t.dictionary
                          ? s.string2buf(t.dictionary)
                          : "[object ArrayBuffer]" === l.call(t.dictionary)
                            ? new Uint8Array(t.dictionary)
                            : t.dictionary),
                      (r = n.deflateSetDictionary(this.strm, m)) !== d)
                    )
                      throw new Error(o[r]);
                    this._dict_set = !0;
                  }
                }
                function m(e, t) {
                  var r = new f(t);
                  if ((r.push(e, !0), r.err)) throw r.msg || o[r.err];
                  return r.result;
                }
                ((f.prototype.push = function (e, t) {
                  var r,
                    o,
                    a = this.strm,
                    c = this.options.chunkSize;
                  if (this.ended) return !1;
                  ((o = t === ~~t ? t : !0 === t ? 4 : 0),
                    "string" == typeof e
                      ? (a.input = s.string2buf(e))
                      : "[object ArrayBuffer]" === l.call(e)
                        ? (a.input = new Uint8Array(e))
                        : (a.input = e),
                    (a.next_in = 0),
                    (a.avail_in = a.input.length));
                  do {
                    if (
                      (0 === a.avail_out &&
                        ((a.output = new i.Buf8(c)),
                        (a.next_out = 0),
                        (a.avail_out = c)),
                      1 !== (r = n.deflate(a, o)) && r !== d)
                    )
                      return (this.onEnd(r), !(this.ended = !0));
                    (0 !== a.avail_out &&
                      (0 !== a.avail_in || (4 !== o && 2 !== o))) ||
                      ("string" === this.options.to
                        ? this.onData(
                            s.buf2binstring(i.shrinkBuf(a.output, a.next_out)),
                          )
                        : this.onData(i.shrinkBuf(a.output, a.next_out)));
                  } while ((0 < a.avail_in || 0 === a.avail_out) && 1 !== r);
                  return 4 === o
                    ? ((r = n.deflateEnd(this.strm)),
                      this.onEnd(r),
                      (this.ended = !0),
                      r === d)
                    : 2 !== o || (this.onEnd(d), !(a.avail_out = 0));
                }),
                  (f.prototype.onData = function (e) {
                    this.chunks.push(e);
                  }),
                  (f.prototype.onEnd = function (e) {
                    (e === d &&
                      ("string" === this.options.to
                        ? (this.result = this.chunks.join(""))
                        : (this.result = i.flattenChunks(this.chunks))),
                      (this.chunks = []),
                      (this.err = e),
                      (this.msg = this.strm.msg));
                  }),
                  (r.Deflate = f),
                  (r.deflate = m),
                  (r.deflateRaw = function (e, t) {
                    return (((t = t || {}).raw = !0), m(e, t));
                  }),
                  (r.gzip = function (e, t) {
                    return (((t = t || {}).gzip = !0), m(e, t));
                  }));
              },
              {
                "./utils/common": 41,
                "./utils/strings": 42,
                "./zlib/deflate": 46,
                "./zlib/messages": 51,
                "./zlib/zstream": 53,
              },
            ],
            40: [
              function (e, t, r) {
                "use strict";
                var n = e("./zlib/inflate"),
                  i = e("./utils/common"),
                  s = e("./utils/strings"),
                  o = e("./zlib/constants"),
                  a = e("./zlib/messages"),
                  l = e("./zlib/zstream"),
                  d = e("./zlib/gzheader"),
                  c = Object.prototype.toString;
                function u(e) {
                  if (!(this instanceof u)) return new u(e);
                  this.options = i.assign(
                    { chunkSize: 16384, windowBits: 0, to: "" },
                    e || {},
                  );
                  var t = this.options;
                  (t.raw &&
                    0 <= t.windowBits &&
                    t.windowBits < 16 &&
                    ((t.windowBits = -t.windowBits),
                    0 === t.windowBits && (t.windowBits = -15)),
                    !(0 <= t.windowBits && t.windowBits < 16) ||
                      (e && e.windowBits) ||
                      (t.windowBits += 32),
                    15 < t.windowBits &&
                      t.windowBits < 48 &&
                      !(15 & t.windowBits) &&
                      (t.windowBits |= 15),
                    (this.err = 0),
                    (this.msg = ""),
                    (this.ended = !1),
                    (this.chunks = []),
                    (this.strm = new l()),
                    (this.strm.avail_out = 0));
                  var r = n.inflateInit2(this.strm, t.windowBits);
                  if (r !== o.Z_OK) throw new Error(a[r]);
                  ((this.header = new d()),
                    n.inflateGetHeader(this.strm, this.header));
                }
                function h(e, t) {
                  var r = new u(t);
                  if ((r.push(e, !0), r.err)) throw r.msg || a[r.err];
                  return r.result;
                }
                ((u.prototype.push = function (e, t) {
                  var r,
                    a,
                    l,
                    d,
                    u,
                    h,
                    f = this.strm,
                    m = this.options.chunkSize,
                    g = this.options.dictionary,
                    p = !1;
                  if (this.ended) return !1;
                  ((a = t === ~~t ? t : !0 === t ? o.Z_FINISH : o.Z_NO_FLUSH),
                    "string" == typeof e
                      ? (f.input = s.binstring2buf(e))
                      : "[object ArrayBuffer]" === c.call(e)
                        ? (f.input = new Uint8Array(e))
                        : (f.input = e),
                    (f.next_in = 0),
                    (f.avail_in = f.input.length));
                  do {
                    if (
                      (0 === f.avail_out &&
                        ((f.output = new i.Buf8(m)),
                        (f.next_out = 0),
                        (f.avail_out = m)),
                      (r = n.inflate(f, o.Z_NO_FLUSH)) === o.Z_NEED_DICT &&
                        g &&
                        ((h =
                          "string" == typeof g
                            ? s.string2buf(g)
                            : "[object ArrayBuffer]" === c.call(g)
                              ? new Uint8Array(g)
                              : g),
                        (r = n.inflateSetDictionary(this.strm, h))),
                      r === o.Z_BUF_ERROR &&
                        !0 === p &&
                        ((r = o.Z_OK), (p = !1)),
                      r !== o.Z_STREAM_END && r !== o.Z_OK)
                    )
                      return (this.onEnd(r), !(this.ended = !0));
                    (f.next_out &&
                      ((0 !== f.avail_out &&
                        r !== o.Z_STREAM_END &&
                        (0 !== f.avail_in ||
                          (a !== o.Z_FINISH && a !== o.Z_SYNC_FLUSH))) ||
                        ("string" === this.options.to
                          ? ((l = s.utf8border(f.output, f.next_out)),
                            (d = f.next_out - l),
                            (u = s.buf2string(f.output, l)),
                            (f.next_out = d),
                            (f.avail_out = m - d),
                            d && i.arraySet(f.output, f.output, l, d, 0),
                            this.onData(u))
                          : this.onData(i.shrinkBuf(f.output, f.next_out)))),
                      0 === f.avail_in && 0 === f.avail_out && (p = !0));
                  } while (
                    (0 < f.avail_in || 0 === f.avail_out) &&
                    r !== o.Z_STREAM_END
                  );
                  return (
                    r === o.Z_STREAM_END && (a = o.Z_FINISH),
                    a === o.Z_FINISH
                      ? ((r = n.inflateEnd(this.strm)),
                        this.onEnd(r),
                        (this.ended = !0),
                        r === o.Z_OK)
                      : a !== o.Z_SYNC_FLUSH ||
                        (this.onEnd(o.Z_OK), !(f.avail_out = 0))
                  );
                }),
                  (u.prototype.onData = function (e) {
                    this.chunks.push(e);
                  }),
                  (u.prototype.onEnd = function (e) {
                    (e === o.Z_OK &&
                      ("string" === this.options.to
                        ? (this.result = this.chunks.join(""))
                        : (this.result = i.flattenChunks(this.chunks))),
                      (this.chunks = []),
                      (this.err = e),
                      (this.msg = this.strm.msg));
                  }),
                  (r.Inflate = u),
                  (r.inflate = h),
                  (r.inflateRaw = function (e, t) {
                    return (((t = t || {}).raw = !0), h(e, t));
                  }),
                  (r.ungzip = h));
              },
              {
                "./utils/common": 41,
                "./utils/strings": 42,
                "./zlib/constants": 44,
                "./zlib/gzheader": 47,
                "./zlib/inflate": 49,
                "./zlib/messages": 51,
                "./zlib/zstream": 53,
              },
            ],
            41: [
              function (e, t, r) {
                "use strict";
                var n =
                  "undefined" != typeof Uint8Array &&
                  "undefined" != typeof Uint16Array &&
                  "undefined" != typeof Int32Array;
                ((r.assign = function (e) {
                  for (
                    var t = Array.prototype.slice.call(arguments, 1);
                    t.length;

                  ) {
                    var r = t.shift();
                    if (r) {
                      if ("object" != typeof r)
                        throw new TypeError(r + "must be non-object");
                      for (var n in r) r.hasOwnProperty(n) && (e[n] = r[n]);
                    }
                  }
                  return e;
                }),
                  (r.shrinkBuf = function (e, t) {
                    return e.length === t
                      ? e
                      : e.subarray
                        ? e.subarray(0, t)
                        : ((e.length = t), e);
                  }));
                var i = {
                    arraySet: function (e, t, r, n, i) {
                      if (t.subarray && e.subarray)
                        e.set(t.subarray(r, r + n), i);
                      else for (var s = 0; s < n; s++) e[i + s] = t[r + s];
                    },
                    flattenChunks: function (e) {
                      var t, r, n, i, s, o;
                      for (t = n = 0, r = e.length; t < r; t++)
                        n += e[t].length;
                      for (
                        o = new Uint8Array(n), t = i = 0, r = e.length;
                        t < r;
                        t++
                      )
                        ((s = e[t]), o.set(s, i), (i += s.length));
                      return o;
                    },
                  },
                  s = {
                    arraySet: function (e, t, r, n, i) {
                      for (var s = 0; s < n; s++) e[i + s] = t[r + s];
                    },
                    flattenChunks: function (e) {
                      return [].concat.apply([], e);
                    },
                  };
                ((r.setTyped = function (e) {
                  e
                    ? ((r.Buf8 = Uint8Array),
                      (r.Buf16 = Uint16Array),
                      (r.Buf32 = Int32Array),
                      r.assign(r, i))
                    : ((r.Buf8 = Array),
                      (r.Buf16 = Array),
                      (r.Buf32 = Array),
                      r.assign(r, s));
                }),
                  r.setTyped(n));
              },
              {},
            ],
            42: [
              function (e, t, r) {
                "use strict";
                var n = e("./common"),
                  i = !0,
                  s = !0;
                try {
                  String.fromCharCode.apply(null, [0]);
                } catch (e) {
                  i = !1;
                }
                try {
                  String.fromCharCode.apply(null, new Uint8Array(1));
                } catch (e) {
                  s = !1;
                }
                for (var o = new n.Buf8(256), a = 0; a < 256; a++)
                  o[a] =
                    252 <= a
                      ? 6
                      : 248 <= a
                        ? 5
                        : 240 <= a
                          ? 4
                          : 224 <= a
                            ? 3
                            : 192 <= a
                              ? 2
                              : 1;
                function l(e, t) {
                  if (t < 65537 && ((e.subarray && s) || (!e.subarray && i)))
                    return String.fromCharCode.apply(null, n.shrinkBuf(e, t));
                  for (var r = "", o = 0; o < t; o++)
                    r += String.fromCharCode(e[o]);
                  return r;
                }
                ((o[254] = o[254] = 1),
                  (r.string2buf = function (e) {
                    var t,
                      r,
                      i,
                      s,
                      o,
                      a = e.length,
                      l = 0;
                    for (s = 0; s < a; s++)
                      (55296 == (64512 & (r = e.charCodeAt(s))) &&
                        s + 1 < a &&
                        56320 == (64512 & (i = e.charCodeAt(s + 1))) &&
                        ((r = 65536 + ((r - 55296) << 10) + (i - 56320)), s++),
                        (l += r < 128 ? 1 : r < 2048 ? 2 : r < 65536 ? 3 : 4));
                    for (t = new n.Buf8(l), s = o = 0; o < l; s++)
                      (55296 == (64512 & (r = e.charCodeAt(s))) &&
                        s + 1 < a &&
                        56320 == (64512 & (i = e.charCodeAt(s + 1))) &&
                        ((r = 65536 + ((r - 55296) << 10) + (i - 56320)), s++),
                        r < 128
                          ? (t[o++] = r)
                          : (r < 2048
                              ? (t[o++] = 192 | (r >>> 6))
                              : (r < 65536
                                  ? (t[o++] = 224 | (r >>> 12))
                                  : ((t[o++] = 240 | (r >>> 18)),
                                    (t[o++] = 128 | ((r >>> 12) & 63))),
                                (t[o++] = 128 | ((r >>> 6) & 63))),
                            (t[o++] = 128 | (63 & r))));
                    return t;
                  }),
                  (r.buf2binstring = function (e) {
                    return l(e, e.length);
                  }),
                  (r.binstring2buf = function (e) {
                    for (
                      var t = new n.Buf8(e.length), r = 0, i = t.length;
                      r < i;
                      r++
                    )
                      t[r] = e.charCodeAt(r);
                    return t;
                  }),
                  (r.buf2string = function (e, t) {
                    var r,
                      n,
                      i,
                      s,
                      a = t || e.length,
                      d = new Array(2 * a);
                    for (r = n = 0; r < a; )
                      if ((i = e[r++]) < 128) d[n++] = i;
                      else if (4 < (s = o[i])) ((d[n++] = 65533), (r += s - 1));
                      else {
                        for (
                          i &= 2 === s ? 31 : 3 === s ? 15 : 7;
                          1 < s && r < a;

                        )
                          ((i = (i << 6) | (63 & e[r++])), s--);
                        1 < s
                          ? (d[n++] = 65533)
                          : i < 65536
                            ? (d[n++] = i)
                            : ((i -= 65536),
                              (d[n++] = 55296 | ((i >> 10) & 1023)),
                              (d[n++] = 56320 | (1023 & i)));
                      }
                    return l(d, n);
                  }),
                  (r.utf8border = function (e, t) {
                    var r;
                    for (
                      (t = t || e.length) > e.length && (t = e.length),
                        r = t - 1;
                      0 <= r && 128 == (192 & e[r]);

                    )
                      r--;
                    return r < 0 || 0 === r ? t : r + o[e[r]] > t ? r : t;
                  }));
              },
              { "./common": 41 },
            ],
            43: [
              function (e, t, r) {
                "use strict";
                t.exports = function (e, t, r, n) {
                  for (
                    var i = 65535 & e, s = (e >>> 16) & 65535, o = 0;
                    0 !== r;

                  ) {
                    for (
                      r -= o = 2e3 < r ? 2e3 : r;
                      (s = (s + (i = (i + t[n++]) | 0)) | 0), --o;

                    );
                    ((i %= 65521), (s %= 65521));
                  }
                  return i | (s << 16);
                };
              },
              {},
            ],
            44: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  Z_NO_FLUSH: 0,
                  Z_PARTIAL_FLUSH: 1,
                  Z_SYNC_FLUSH: 2,
                  Z_FULL_FLUSH: 3,
                  Z_FINISH: 4,
                  Z_BLOCK: 5,
                  Z_TREES: 6,
                  Z_OK: 0,
                  Z_STREAM_END: 1,
                  Z_NEED_DICT: 2,
                  Z_ERRNO: -1,
                  Z_STREAM_ERROR: -2,
                  Z_DATA_ERROR: -3,
                  Z_BUF_ERROR: -5,
                  Z_NO_COMPRESSION: 0,
                  Z_BEST_SPEED: 1,
                  Z_BEST_COMPRESSION: 9,
                  Z_DEFAULT_COMPRESSION: -1,
                  Z_FILTERED: 1,
                  Z_HUFFMAN_ONLY: 2,
                  Z_RLE: 3,
                  Z_FIXED: 4,
                  Z_DEFAULT_STRATEGY: 0,
                  Z_BINARY: 0,
                  Z_TEXT: 1,
                  Z_UNKNOWN: 2,
                  Z_DEFLATED: 8,
                };
              },
              {},
            ],
            45: [
              function (e, t, r) {
                "use strict";
                var n = (function () {
                  for (var e, t = [], r = 0; r < 256; r++) {
                    e = r;
                    for (var n = 0; n < 8; n++)
                      e = 1 & e ? 3988292384 ^ (e >>> 1) : e >>> 1;
                    t[r] = e;
                  }
                  return t;
                })();
                t.exports = function (e, t, r, i) {
                  var s = n,
                    o = i + r;
                  e ^= -1;
                  for (var a = i; a < o; a++)
                    e = (e >>> 8) ^ s[255 & (e ^ t[a])];
                  return ~e;
                };
              },
              {},
            ],
            46: [
              function (e, t, r) {
                "use strict";
                var n,
                  i = e("../utils/common"),
                  s = e("./trees"),
                  o = e("./adler32"),
                  a = e("./crc32"),
                  l = e("./messages"),
                  d = 0,
                  c = 4,
                  u = 0,
                  h = -2,
                  f = -1,
                  m = 4,
                  g = 2,
                  p = 8,
                  y = 9,
                  b = 286,
                  w = 30,
                  v = 19,
                  A = 2 * b + 1,
                  _ = 15,
                  x = 3,
                  k = 258,
                  S = k + x + 1,
                  C = 42,
                  E = 113,
                  O = 1,
                  I = 2,
                  T = 3,
                  B = 4;
                function D(e, t) {
                  return ((e.msg = l[t]), t);
                }
                function z(e) {
                  return (e << 1) - (4 < e ? 9 : 0);
                }
                function P(e) {
                  for (var t = e.length; 0 <= --t; ) e[t] = 0;
                }
                function F(e) {
                  var t = e.state,
                    r = t.pending;
                  (r > e.avail_out && (r = e.avail_out),
                    0 !== r &&
                      (i.arraySet(
                        e.output,
                        t.pending_buf,
                        t.pending_out,
                        r,
                        e.next_out,
                      ),
                      (e.next_out += r),
                      (t.pending_out += r),
                      (e.total_out += r),
                      (e.avail_out -= r),
                      (t.pending -= r),
                      0 === t.pending && (t.pending_out = 0)));
                }
                function L(e, t) {
                  (s._tr_flush_block(
                    e,
                    0 <= e.block_start ? e.block_start : -1,
                    e.strstart - e.block_start,
                    t,
                  ),
                    (e.block_start = e.strstart),
                    F(e.strm));
                }
                function U(e, t) {
                  e.pending_buf[e.pending++] = t;
                }
                function R(e, t) {
                  ((e.pending_buf[e.pending++] = (t >>> 8) & 255),
                    (e.pending_buf[e.pending++] = 255 & t));
                }
                function N(e, t) {
                  var r,
                    n,
                    i = e.max_chain_length,
                    s = e.strstart,
                    o = e.prev_length,
                    a = e.nice_match,
                    l =
                      e.strstart > e.w_size - S
                        ? e.strstart - (e.w_size - S)
                        : 0,
                    d = e.window,
                    c = e.w_mask,
                    u = e.prev,
                    h = e.strstart + k,
                    f = d[s + o - 1],
                    m = d[s + o];
                  (e.prev_length >= e.good_match && (i >>= 2),
                    a > e.lookahead && (a = e.lookahead));
                  do {
                    if (
                      d[(r = t) + o] === m &&
                      d[r + o - 1] === f &&
                      d[r] === d[s] &&
                      d[++r] === d[s + 1]
                    ) {
                      ((s += 2), r++);
                      do {} while (
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        d[++s] === d[++r] &&
                        s < h
                      );
                      if (((n = k - (h - s)), (s = h - k), o < n)) {
                        if (((e.match_start = t), a <= (o = n))) break;
                        ((f = d[s + o - 1]), (m = d[s + o]));
                      }
                    }
                  } while ((t = u[t & c]) > l && 0 != --i);
                  return o <= e.lookahead ? o : e.lookahead;
                }
                function M(e) {
                  var t,
                    r,
                    n,
                    s,
                    l,
                    d,
                    c,
                    u,
                    h,
                    f,
                    m = e.w_size;
                  do {
                    if (
                      ((s = e.window_size - e.lookahead - e.strstart),
                      e.strstart >= m + (m - S))
                    ) {
                      for (
                        i.arraySet(e.window, e.window, m, m, 0),
                          e.match_start -= m,
                          e.strstart -= m,
                          e.block_start -= m,
                          t = r = e.hash_size;
                        (n = e.head[--t]),
                          (e.head[t] = m <= n ? n - m : 0),
                          --r;

                      );
                      for (
                        t = r = m;
                        (n = e.prev[--t]),
                          (e.prev[t] = m <= n ? n - m : 0),
                          --r;

                      );
                      s += m;
                    }
                    if (0 === e.strm.avail_in) break;
                    if (
                      ((d = e.strm),
                      (c = e.window),
                      (u = e.strstart + e.lookahead),
                      (f = void 0),
                      (h = s) < (f = d.avail_in) && (f = h),
                      (r =
                        0 === f
                          ? 0
                          : ((d.avail_in -= f),
                            i.arraySet(c, d.input, d.next_in, f, u),
                            1 === d.state.wrap
                              ? (d.adler = o(d.adler, c, f, u))
                              : 2 === d.state.wrap &&
                                (d.adler = a(d.adler, c, f, u)),
                            (d.next_in += f),
                            (d.total_in += f),
                            f)),
                      (e.lookahead += r),
                      e.lookahead + e.insert >= x)
                    )
                      for (
                        l = e.strstart - e.insert,
                          e.ins_h = e.window[l],
                          e.ins_h =
                            ((e.ins_h << e.hash_shift) ^ e.window[l + 1]) &
                            e.hash_mask;
                        e.insert &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^ e.window[l + x - 1]) &
                          e.hash_mask),
                        (e.prev[l & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = l),
                        l++,
                        e.insert--,
                        !(e.lookahead + e.insert < x));

                      );
                  } while (e.lookahead < S && 0 !== e.strm.avail_in);
                }
                function j(e, t) {
                  for (var r, n; ; ) {
                    if (e.lookahead < S) {
                      if ((M(e), e.lookahead < S && t === d)) return O;
                      if (0 === e.lookahead) break;
                    }
                    if (
                      ((r = 0),
                      e.lookahead >= x &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^
                            e.window[e.strstart + x - 1]) &
                          e.hash_mask),
                        (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = e.strstart)),
                      0 !== r &&
                        e.strstart - r <= e.w_size - S &&
                        (e.match_length = N(e, r)),
                      e.match_length >= x)
                    )
                      if (
                        ((n = s._tr_tally(
                          e,
                          e.strstart - e.match_start,
                          e.match_length - x,
                        )),
                        (e.lookahead -= e.match_length),
                        e.match_length <= e.max_lazy_match && e.lookahead >= x)
                      ) {
                        for (
                          e.match_length--;
                          e.strstart++,
                            (e.ins_h =
                              ((e.ins_h << e.hash_shift) ^
                                e.window[e.strstart + x - 1]) &
                              e.hash_mask),
                            (r = e.prev[e.strstart & e.w_mask] =
                              e.head[e.ins_h]),
                            (e.head[e.ins_h] = e.strstart),
                            0 != --e.match_length;

                        );
                        e.strstart++;
                      } else
                        ((e.strstart += e.match_length),
                          (e.match_length = 0),
                          (e.ins_h = e.window[e.strstart]),
                          (e.ins_h =
                            ((e.ins_h << e.hash_shift) ^
                              e.window[e.strstart + 1]) &
                            e.hash_mask));
                    else
                      ((n = s._tr_tally(e, 0, e.window[e.strstart])),
                        e.lookahead--,
                        e.strstart++);
                    if (n && (L(e, !1), 0 === e.strm.avail_out)) return O;
                  }
                  return (
                    (e.insert = e.strstart < x - 1 ? e.strstart : x - 1),
                    t === c
                      ? (L(e, !0), 0 === e.strm.avail_out ? T : B)
                      : e.last_lit && (L(e, !1), 0 === e.strm.avail_out)
                        ? O
                        : I
                  );
                }
                function W(e, t) {
                  for (var r, n, i; ; ) {
                    if (e.lookahead < S) {
                      if ((M(e), e.lookahead < S && t === d)) return O;
                      if (0 === e.lookahead) break;
                    }
                    if (
                      ((r = 0),
                      e.lookahead >= x &&
                        ((e.ins_h =
                          ((e.ins_h << e.hash_shift) ^
                            e.window[e.strstart + x - 1]) &
                          e.hash_mask),
                        (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                        (e.head[e.ins_h] = e.strstart)),
                      (e.prev_length = e.match_length),
                      (e.prev_match = e.match_start),
                      (e.match_length = x - 1),
                      0 !== r &&
                        e.prev_length < e.max_lazy_match &&
                        e.strstart - r <= e.w_size - S &&
                        ((e.match_length = N(e, r)),
                        e.match_length <= 5 &&
                          (1 === e.strategy ||
                            (e.match_length === x &&
                              4096 < e.strstart - e.match_start)) &&
                          (e.match_length = x - 1)),
                      e.prev_length >= x && e.match_length <= e.prev_length)
                    ) {
                      for (
                        i = e.strstart + e.lookahead - x,
                          n = s._tr_tally(
                            e,
                            e.strstart - 1 - e.prev_match,
                            e.prev_length - x,
                          ),
                          e.lookahead -= e.prev_length - 1,
                          e.prev_length -= 2;
                        ++e.strstart <= i &&
                          ((e.ins_h =
                            ((e.ins_h << e.hash_shift) ^
                              e.window[e.strstart + x - 1]) &
                            e.hash_mask),
                          (r = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h]),
                          (e.head[e.ins_h] = e.strstart)),
                          0 != --e.prev_length;

                      );
                      if (
                        ((e.match_available = 0),
                        (e.match_length = x - 1),
                        e.strstart++,
                        n && (L(e, !1), 0 === e.strm.avail_out))
                      )
                        return O;
                    } else if (e.match_available) {
                      if (
                        ((n = s._tr_tally(e, 0, e.window[e.strstart - 1])) &&
                          L(e, !1),
                        e.strstart++,
                        e.lookahead--,
                        0 === e.strm.avail_out)
                      )
                        return O;
                    } else
                      ((e.match_available = 1), e.strstart++, e.lookahead--);
                  }
                  return (
                    e.match_available &&
                      ((n = s._tr_tally(e, 0, e.window[e.strstart - 1])),
                      (e.match_available = 0)),
                    (e.insert = e.strstart < x - 1 ? e.strstart : x - 1),
                    t === c
                      ? (L(e, !0), 0 === e.strm.avail_out ? T : B)
                      : e.last_lit && (L(e, !1), 0 === e.strm.avail_out)
                        ? O
                        : I
                  );
                }
                function $(e, t, r, n, i) {
                  ((this.good_length = e),
                    (this.max_lazy = t),
                    (this.nice_length = r),
                    (this.max_chain = n),
                    (this.func = i));
                }
                function Z() {
                  ((this.strm = null),
                    (this.status = 0),
                    (this.pending_buf = null),
                    (this.pending_buf_size = 0),
                    (this.pending_out = 0),
                    (this.pending = 0),
                    (this.wrap = 0),
                    (this.gzhead = null),
                    (this.gzindex = 0),
                    (this.method = p),
                    (this.last_flush = -1),
                    (this.w_size = 0),
                    (this.w_bits = 0),
                    (this.w_mask = 0),
                    (this.window = null),
                    (this.window_size = 0),
                    (this.prev = null),
                    (this.head = null),
                    (this.ins_h = 0),
                    (this.hash_size = 0),
                    (this.hash_bits = 0),
                    (this.hash_mask = 0),
                    (this.hash_shift = 0),
                    (this.block_start = 0),
                    (this.match_length = 0),
                    (this.prev_match = 0),
                    (this.match_available = 0),
                    (this.strstart = 0),
                    (this.match_start = 0),
                    (this.lookahead = 0),
                    (this.prev_length = 0),
                    (this.max_chain_length = 0),
                    (this.max_lazy_match = 0),
                    (this.level = 0),
                    (this.strategy = 0),
                    (this.good_match = 0),
                    (this.nice_match = 0),
                    (this.dyn_ltree = new i.Buf16(2 * A)),
                    (this.dyn_dtree = new i.Buf16(2 * (2 * w + 1))),
                    (this.bl_tree = new i.Buf16(2 * (2 * v + 1))),
                    P(this.dyn_ltree),
                    P(this.dyn_dtree),
                    P(this.bl_tree),
                    (this.l_desc = null),
                    (this.d_desc = null),
                    (this.bl_desc = null),
                    (this.bl_count = new i.Buf16(_ + 1)),
                    (this.heap = new i.Buf16(2 * b + 1)),
                    P(this.heap),
                    (this.heap_len = 0),
                    (this.heap_max = 0),
                    (this.depth = new i.Buf16(2 * b + 1)),
                    P(this.depth),
                    (this.l_buf = 0),
                    (this.lit_bufsize = 0),
                    (this.last_lit = 0),
                    (this.d_buf = 0),
                    (this.opt_len = 0),
                    (this.static_len = 0),
                    (this.matches = 0),
                    (this.insert = 0),
                    (this.bi_buf = 0),
                    (this.bi_valid = 0));
                }
                function V(e) {
                  var t;
                  return e && e.state
                    ? ((e.total_in = e.total_out = 0),
                      (e.data_type = g),
                      ((t = e.state).pending = 0),
                      (t.pending_out = 0),
                      t.wrap < 0 && (t.wrap = -t.wrap),
                      (t.status = t.wrap ? C : E),
                      (e.adler = 2 === t.wrap ? 0 : 1),
                      (t.last_flush = d),
                      s._tr_init(t),
                      u)
                    : D(e, h);
                }
                function H(e) {
                  var t = V(e);
                  return (
                    t === u &&
                      (function (e) {
                        ((e.window_size = 2 * e.w_size),
                          P(e.head),
                          (e.max_lazy_match = n[e.level].max_lazy),
                          (e.good_match = n[e.level].good_length),
                          (e.nice_match = n[e.level].nice_length),
                          (e.max_chain_length = n[e.level].max_chain),
                          (e.strstart = 0),
                          (e.block_start = 0),
                          (e.lookahead = 0),
                          (e.insert = 0),
                          (e.match_length = e.prev_length = x - 1),
                          (e.match_available = 0),
                          (e.ins_h = 0));
                      })(e.state),
                    t
                  );
                }
                function K(e, t, r, n, s, o) {
                  if (!e) return h;
                  var a = 1;
                  if (
                    (t === f && (t = 6),
                    n < 0
                      ? ((a = 0), (n = -n))
                      : 15 < n && ((a = 2), (n -= 16)),
                    s < 1 ||
                      y < s ||
                      r !== p ||
                      n < 8 ||
                      15 < n ||
                      t < 0 ||
                      9 < t ||
                      o < 0 ||
                      m < o)
                  )
                    return D(e, h);
                  8 === n && (n = 9);
                  var l = new Z();
                  return (
                    ((e.state = l).strm = e),
                    (l.wrap = a),
                    (l.gzhead = null),
                    (l.w_bits = n),
                    (l.w_size = 1 << l.w_bits),
                    (l.w_mask = l.w_size - 1),
                    (l.hash_bits = s + 7),
                    (l.hash_size = 1 << l.hash_bits),
                    (l.hash_mask = l.hash_size - 1),
                    (l.hash_shift = ~~((l.hash_bits + x - 1) / x)),
                    (l.window = new i.Buf8(2 * l.w_size)),
                    (l.head = new i.Buf16(l.hash_size)),
                    (l.prev = new i.Buf16(l.w_size)),
                    (l.lit_bufsize = 1 << (s + 6)),
                    (l.pending_buf_size = 4 * l.lit_bufsize),
                    (l.pending_buf = new i.Buf8(l.pending_buf_size)),
                    (l.d_buf = 1 * l.lit_bufsize),
                    (l.l_buf = 3 * l.lit_bufsize),
                    (l.level = t),
                    (l.strategy = o),
                    (l.method = r),
                    H(e)
                  );
                }
                ((n = [
                  new $(0, 0, 0, 0, function (e, t) {
                    var r = 65535;
                    for (
                      r > e.pending_buf_size - 5 &&
                      (r = e.pending_buf_size - 5);
                      ;

                    ) {
                      if (e.lookahead <= 1) {
                        if ((M(e), 0 === e.lookahead && t === d)) return O;
                        if (0 === e.lookahead) break;
                      }
                      ((e.strstart += e.lookahead), (e.lookahead = 0));
                      var n = e.block_start + r;
                      if (
                        (0 === e.strstart || e.strstart >= n) &&
                        ((e.lookahead = e.strstart - n),
                        (e.strstart = n),
                        L(e, !1),
                        0 === e.strm.avail_out)
                      )
                        return O;
                      if (
                        e.strstart - e.block_start >= e.w_size - S &&
                        (L(e, !1), 0 === e.strm.avail_out)
                      )
                        return O;
                    }
                    return (
                      (e.insert = 0),
                      t === c
                        ? (L(e, !0), 0 === e.strm.avail_out ? T : B)
                        : (e.strstart > e.block_start &&
                            (L(e, !1), e.strm.avail_out),
                          O)
                    );
                  }),
                  new $(4, 4, 8, 4, j),
                  new $(4, 5, 16, 8, j),
                  new $(4, 6, 32, 32, j),
                  new $(4, 4, 16, 16, W),
                  new $(8, 16, 32, 32, W),
                  new $(8, 16, 128, 128, W),
                  new $(8, 32, 128, 256, W),
                  new $(32, 128, 258, 1024, W),
                  new $(32, 258, 258, 4096, W),
                ]),
                  (r.deflateInit = function (e, t) {
                    return K(e, t, p, 15, 8, 0);
                  }),
                  (r.deflateInit2 = K),
                  (r.deflateReset = H),
                  (r.deflateResetKeep = V),
                  (r.deflateSetHeader = function (e, t) {
                    return e && e.state
                      ? 2 !== e.state.wrap
                        ? h
                        : ((e.state.gzhead = t), u)
                      : h;
                  }),
                  (r.deflate = function (e, t) {
                    var r, i, o, l;
                    if (!e || !e.state || 5 < t || t < 0)
                      return e ? D(e, h) : h;
                    if (
                      ((i = e.state),
                      !e.output ||
                        (!e.input && 0 !== e.avail_in) ||
                        (666 === i.status && t !== c))
                    )
                      return D(e, 0 === e.avail_out ? -5 : h);
                    if (
                      ((i.strm = e),
                      (r = i.last_flush),
                      (i.last_flush = t),
                      i.status === C)
                    )
                      if (2 === i.wrap)
                        ((e.adler = 0),
                          U(i, 31),
                          U(i, 139),
                          U(i, 8),
                          i.gzhead
                            ? (U(
                                i,
                                (i.gzhead.text ? 1 : 0) +
                                  (i.gzhead.hcrc ? 2 : 0) +
                                  (i.gzhead.extra ? 4 : 0) +
                                  (i.gzhead.name ? 8 : 0) +
                                  (i.gzhead.comment ? 16 : 0),
                              ),
                              U(i, 255 & i.gzhead.time),
                              U(i, (i.gzhead.time >> 8) & 255),
                              U(i, (i.gzhead.time >> 16) & 255),
                              U(i, (i.gzhead.time >> 24) & 255),
                              U(
                                i,
                                9 === i.level
                                  ? 2
                                  : 2 <= i.strategy || i.level < 2
                                    ? 4
                                    : 0,
                              ),
                              U(i, 255 & i.gzhead.os),
                              i.gzhead.extra &&
                                i.gzhead.extra.length &&
                                (U(i, 255 & i.gzhead.extra.length),
                                U(i, (i.gzhead.extra.length >> 8) & 255)),
                              i.gzhead.hcrc &&
                                (e.adler = a(
                                  e.adler,
                                  i.pending_buf,
                                  i.pending,
                                  0,
                                )),
                              (i.gzindex = 0),
                              (i.status = 69))
                            : (U(i, 0),
                              U(i, 0),
                              U(i, 0),
                              U(i, 0),
                              U(i, 0),
                              U(
                                i,
                                9 === i.level
                                  ? 2
                                  : 2 <= i.strategy || i.level < 2
                                    ? 4
                                    : 0,
                              ),
                              U(i, 3),
                              (i.status = E)));
                      else {
                        var f = (p + ((i.w_bits - 8) << 4)) << 8;
                        ((f |=
                          (2 <= i.strategy || i.level < 2
                            ? 0
                            : i.level < 6
                              ? 1
                              : 6 === i.level
                                ? 2
                                : 3) << 6),
                          0 !== i.strstart && (f |= 32),
                          (f += 31 - (f % 31)),
                          (i.status = E),
                          R(i, f),
                          0 !== i.strstart &&
                            (R(i, e.adler >>> 16), R(i, 65535 & e.adler)),
                          (e.adler = 1));
                      }
                    if (69 === i.status)
                      if (i.gzhead.extra) {
                        for (
                          o = i.pending;
                          i.gzindex < (65535 & i.gzhead.extra.length) &&
                          (i.pending !== i.pending_buf_size ||
                            (i.gzhead.hcrc &&
                              i.pending > o &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - o,
                                o,
                              )),
                            F(e),
                            (o = i.pending),
                            i.pending !== i.pending_buf_size));

                        )
                          (U(i, 255 & i.gzhead.extra[i.gzindex]), i.gzindex++);
                        (i.gzhead.hcrc &&
                          i.pending > o &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - o,
                            o,
                          )),
                          i.gzindex === i.gzhead.extra.length &&
                            ((i.gzindex = 0), (i.status = 73)));
                      } else i.status = 73;
                    if (73 === i.status)
                      if (i.gzhead.name) {
                        o = i.pending;
                        do {
                          if (
                            i.pending === i.pending_buf_size &&
                            (i.gzhead.hcrc &&
                              i.pending > o &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - o,
                                o,
                              )),
                            F(e),
                            (o = i.pending),
                            i.pending === i.pending_buf_size)
                          ) {
                            l = 1;
                            break;
                          }
                          ((l =
                            i.gzindex < i.gzhead.name.length
                              ? 255 & i.gzhead.name.charCodeAt(i.gzindex++)
                              : 0),
                            U(i, l));
                        } while (0 !== l);
                        (i.gzhead.hcrc &&
                          i.pending > o &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - o,
                            o,
                          )),
                          0 === l && ((i.gzindex = 0), (i.status = 91)));
                      } else i.status = 91;
                    if (91 === i.status)
                      if (i.gzhead.comment) {
                        o = i.pending;
                        do {
                          if (
                            i.pending === i.pending_buf_size &&
                            (i.gzhead.hcrc &&
                              i.pending > o &&
                              (e.adler = a(
                                e.adler,
                                i.pending_buf,
                                i.pending - o,
                                o,
                              )),
                            F(e),
                            (o = i.pending),
                            i.pending === i.pending_buf_size)
                          ) {
                            l = 1;
                            break;
                          }
                          ((l =
                            i.gzindex < i.gzhead.comment.length
                              ? 255 & i.gzhead.comment.charCodeAt(i.gzindex++)
                              : 0),
                            U(i, l));
                        } while (0 !== l);
                        (i.gzhead.hcrc &&
                          i.pending > o &&
                          (e.adler = a(
                            e.adler,
                            i.pending_buf,
                            i.pending - o,
                            o,
                          )),
                          0 === l && (i.status = 103));
                      } else i.status = 103;
                    if (
                      (103 === i.status &&
                        (i.gzhead.hcrc
                          ? (i.pending + 2 > i.pending_buf_size && F(e),
                            i.pending + 2 <= i.pending_buf_size &&
                              (U(i, 255 & e.adler),
                              U(i, (e.adler >> 8) & 255),
                              (e.adler = 0),
                              (i.status = E)))
                          : (i.status = E)),
                      0 !== i.pending)
                    ) {
                      if ((F(e), 0 === e.avail_out))
                        return ((i.last_flush = -1), u);
                    } else if (0 === e.avail_in && z(t) <= z(r) && t !== c)
                      return D(e, -5);
                    if (666 === i.status && 0 !== e.avail_in) return D(e, -5);
                    if (
                      0 !== e.avail_in ||
                      0 !== i.lookahead ||
                      (t !== d && 666 !== i.status)
                    ) {
                      var m =
                        2 === i.strategy
                          ? (function (e, t) {
                              for (var r; ; ) {
                                if (
                                  0 === e.lookahead &&
                                  (M(e), 0 === e.lookahead)
                                ) {
                                  if (t === d) return O;
                                  break;
                                }
                                if (
                                  ((e.match_length = 0),
                                  (r = s._tr_tally(e, 0, e.window[e.strstart])),
                                  e.lookahead--,
                                  e.strstart++,
                                  r && (L(e, !1), 0 === e.strm.avail_out))
                                )
                                  return O;
                              }
                              return (
                                (e.insert = 0),
                                t === c
                                  ? (L(e, !0), 0 === e.strm.avail_out ? T : B)
                                  : e.last_lit &&
                                      (L(e, !1), 0 === e.strm.avail_out)
                                    ? O
                                    : I
                              );
                            })(i, t)
                          : 3 === i.strategy
                            ? (function (e, t) {
                                for (var r, n, i, o, a = e.window; ; ) {
                                  if (e.lookahead <= k) {
                                    if ((M(e), e.lookahead <= k && t === d))
                                      return O;
                                    if (0 === e.lookahead) break;
                                  }
                                  if (
                                    ((e.match_length = 0),
                                    e.lookahead >= x &&
                                      0 < e.strstart &&
                                      (n = a[(i = e.strstart - 1)]) ===
                                        a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i])
                                  ) {
                                    o = e.strstart + k;
                                    do {} while (
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      n === a[++i] &&
                                      i < o
                                    );
                                    ((e.match_length = k - (o - i)),
                                      e.match_length > e.lookahead &&
                                        (e.match_length = e.lookahead));
                                  }
                                  if (
                                    (e.match_length >= x
                                      ? ((r = s._tr_tally(
                                          e,
                                          1,
                                          e.match_length - x,
                                        )),
                                        (e.lookahead -= e.match_length),
                                        (e.strstart += e.match_length),
                                        (e.match_length = 0))
                                      : ((r = s._tr_tally(
                                          e,
                                          0,
                                          e.window[e.strstart],
                                        )),
                                        e.lookahead--,
                                        e.strstart++),
                                    r && (L(e, !1), 0 === e.strm.avail_out))
                                  )
                                    return O;
                                }
                                return (
                                  (e.insert = 0),
                                  t === c
                                    ? (L(e, !0), 0 === e.strm.avail_out ? T : B)
                                    : e.last_lit &&
                                        (L(e, !1), 0 === e.strm.avail_out)
                                      ? O
                                      : I
                                );
                              })(i, t)
                            : n[i.level].func(i, t);
                      if (
                        ((m !== T && m !== B) || (i.status = 666),
                        m === O || m === T)
                      )
                        return (0 === e.avail_out && (i.last_flush = -1), u);
                      if (
                        m === I &&
                        (1 === t
                          ? s._tr_align(i)
                          : 5 !== t &&
                            (s._tr_stored_block(i, 0, 0, !1),
                            3 === t &&
                              (P(i.head),
                              0 === i.lookahead &&
                                ((i.strstart = 0),
                                (i.block_start = 0),
                                (i.insert = 0)))),
                        F(e),
                        0 === e.avail_out)
                      )
                        return ((i.last_flush = -1), u);
                    }
                    return t !== c
                      ? u
                      : i.wrap <= 0
                        ? 1
                        : (2 === i.wrap
                            ? (U(i, 255 & e.adler),
                              U(i, (e.adler >> 8) & 255),
                              U(i, (e.adler >> 16) & 255),
                              U(i, (e.adler >> 24) & 255),
                              U(i, 255 & e.total_in),
                              U(i, (e.total_in >> 8) & 255),
                              U(i, (e.total_in >> 16) & 255),
                              U(i, (e.total_in >> 24) & 255))
                            : (R(i, e.adler >>> 16), R(i, 65535 & e.adler)),
                          F(e),
                          0 < i.wrap && (i.wrap = -i.wrap),
                          0 !== i.pending ? u : 1);
                  }),
                  (r.deflateEnd = function (e) {
                    var t;
                    return e && e.state
                      ? (t = e.state.status) !== C &&
                        69 !== t &&
                        73 !== t &&
                        91 !== t &&
                        103 !== t &&
                        t !== E &&
                        666 !== t
                        ? D(e, h)
                        : ((e.state = null), t === E ? D(e, -3) : u)
                      : h;
                  }),
                  (r.deflateSetDictionary = function (e, t) {
                    var r,
                      n,
                      s,
                      a,
                      l,
                      d,
                      c,
                      f,
                      m = t.length;
                    if (!e || !e.state) return h;
                    if (
                      2 === (a = (r = e.state).wrap) ||
                      (1 === a && r.status !== C) ||
                      r.lookahead
                    )
                      return h;
                    for (
                      1 === a && (e.adler = o(e.adler, t, m, 0)),
                        r.wrap = 0,
                        m >= r.w_size &&
                          (0 === a &&
                            (P(r.head),
                            (r.strstart = 0),
                            (r.block_start = 0),
                            (r.insert = 0)),
                          (f = new i.Buf8(r.w_size)),
                          i.arraySet(f, t, m - r.w_size, r.w_size, 0),
                          (t = f),
                          (m = r.w_size)),
                        l = e.avail_in,
                        d = e.next_in,
                        c = e.input,
                        e.avail_in = m,
                        e.next_in = 0,
                        e.input = t,
                        M(r);
                      r.lookahead >= x;

                    ) {
                      for (
                        n = r.strstart, s = r.lookahead - (x - 1);
                        (r.ins_h =
                          ((r.ins_h << r.hash_shift) ^ r.window[n + x - 1]) &
                          r.hash_mask),
                          (r.prev[n & r.w_mask] = r.head[r.ins_h]),
                          (r.head[r.ins_h] = n),
                          n++,
                          --s;

                      );
                      ((r.strstart = n), (r.lookahead = x - 1), M(r));
                    }
                    return (
                      (r.strstart += r.lookahead),
                      (r.block_start = r.strstart),
                      (r.insert = r.lookahead),
                      (r.lookahead = 0),
                      (r.match_length = r.prev_length = x - 1),
                      (r.match_available = 0),
                      (e.next_in = d),
                      (e.input = c),
                      (e.avail_in = l),
                      (r.wrap = a),
                      u
                    );
                  }),
                  (r.deflateInfo = "pako deflate (from Nodeca project)"));
              },
              {
                "../utils/common": 41,
                "./adler32": 43,
                "./crc32": 45,
                "./messages": 51,
                "./trees": 52,
              },
            ],
            47: [
              function (e, t, r) {
                "use strict";
                t.exports = function () {
                  ((this.text = 0),
                    (this.time = 0),
                    (this.xflags = 0),
                    (this.os = 0),
                    (this.extra = null),
                    (this.extra_len = 0),
                    (this.name = ""),
                    (this.comment = ""),
                    (this.hcrc = 0),
                    (this.done = !1));
                };
              },
              {},
            ],
            48: [
              function (e, t, r) {
                "use strict";
                t.exports = function (e, t) {
                  var r,
                    n,
                    i,
                    s,
                    o,
                    a,
                    l,
                    d,
                    c,
                    u,
                    h,
                    f,
                    m,
                    g,
                    p,
                    y,
                    b,
                    w,
                    v,
                    A,
                    _,
                    x,
                    k,
                    S,
                    C;
                  ((r = e.state),
                    (n = e.next_in),
                    (S = e.input),
                    (i = n + (e.avail_in - 5)),
                    (s = e.next_out),
                    (C = e.output),
                    (o = s - (t - e.avail_out)),
                    (a = s + (e.avail_out - 257)),
                    (l = r.dmax),
                    (d = r.wsize),
                    (c = r.whave),
                    (u = r.wnext),
                    (h = r.window),
                    (f = r.hold),
                    (m = r.bits),
                    (g = r.lencode),
                    (p = r.distcode),
                    (y = (1 << r.lenbits) - 1),
                    (b = (1 << r.distbits) - 1));
                  e: do {
                    (m < 15 &&
                      ((f += S[n++] << m),
                      (m += 8),
                      (f += S[n++] << m),
                      (m += 8)),
                      (w = g[f & y]));
                    t: for (;;) {
                      if (
                        ((f >>>= v = w >>> 24),
                        (m -= v),
                        0 == (v = (w >>> 16) & 255))
                      )
                        C[s++] = 65535 & w;
                      else {
                        if (!(16 & v)) {
                          if (!(64 & v)) {
                            w = g[(65535 & w) + (f & ((1 << v) - 1))];
                            continue t;
                          }
                          if (32 & v) {
                            r.mode = 12;
                            break e;
                          }
                          ((e.msg = "invalid literal/length code"),
                            (r.mode = 30));
                          break e;
                        }
                        ((A = 65535 & w),
                          (v &= 15) &&
                            (m < v && ((f += S[n++] << m), (m += 8)),
                            (A += f & ((1 << v) - 1)),
                            (f >>>= v),
                            (m -= v)),
                          m < 15 &&
                            ((f += S[n++] << m),
                            (m += 8),
                            (f += S[n++] << m),
                            (m += 8)),
                          (w = p[f & b]));
                        r: for (;;) {
                          if (
                            ((f >>>= v = w >>> 24),
                            (m -= v),
                            !(16 & (v = (w >>> 16) & 255)))
                          ) {
                            if (!(64 & v)) {
                              w = p[(65535 & w) + (f & ((1 << v) - 1))];
                              continue r;
                            }
                            ((e.msg = "invalid distance code"), (r.mode = 30));
                            break e;
                          }
                          if (
                            ((_ = 65535 & w),
                            m < (v &= 15) &&
                              ((f += S[n++] << m),
                              (m += 8) < v && ((f += S[n++] << m), (m += 8))),
                            l < (_ += f & ((1 << v) - 1)))
                          ) {
                            ((e.msg = "invalid distance too far back"),
                              (r.mode = 30));
                            break e;
                          }
                          if (((f >>>= v), (m -= v), (v = s - o) < _)) {
                            if (c < (v = _ - v) && r.sane) {
                              ((e.msg = "invalid distance too far back"),
                                (r.mode = 30));
                              break e;
                            }
                            if (((k = h), (x = 0) === u)) {
                              if (((x += d - v), v < A)) {
                                for (A -= v; (C[s++] = h[x++]), --v; );
                                ((x = s - _), (k = C));
                              }
                            } else if (u < v) {
                              if (((x += d + u - v), (v -= u) < A)) {
                                for (A -= v; (C[s++] = h[x++]), --v; );
                                if (((x = 0), u < A)) {
                                  for (A -= v = u; (C[s++] = h[x++]), --v; );
                                  ((x = s - _), (k = C));
                                }
                              }
                            } else if (((x += u - v), v < A)) {
                              for (A -= v; (C[s++] = h[x++]), --v; );
                              ((x = s - _), (k = C));
                            }
                            for (; 2 < A; )
                              ((C[s++] = k[x++]),
                                (C[s++] = k[x++]),
                                (C[s++] = k[x++]),
                                (A -= 3));
                            A &&
                              ((C[s++] = k[x++]), 1 < A && (C[s++] = k[x++]));
                          } else {
                            for (
                              x = s - _;
                              (C[s++] = C[x++]),
                                (C[s++] = C[x++]),
                                (C[s++] = C[x++]),
                                2 < (A -= 3);

                            );
                            A &&
                              ((C[s++] = C[x++]), 1 < A && (C[s++] = C[x++]));
                          }
                          break;
                        }
                      }
                      break;
                    }
                  } while (n < i && s < a);
                  ((n -= A = m >> 3),
                    (f &= (1 << (m -= A << 3)) - 1),
                    (e.next_in = n),
                    (e.next_out = s),
                    (e.avail_in = n < i ? i - n + 5 : 5 - (n - i)),
                    (e.avail_out = s < a ? a - s + 257 : 257 - (s - a)),
                    (r.hold = f),
                    (r.bits = m));
                };
              },
              {},
            ],
            49: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = e("./adler32"),
                  s = e("./crc32"),
                  o = e("./inffast"),
                  a = e("./inftrees"),
                  l = 1,
                  d = 2,
                  c = 0,
                  u = -2,
                  h = 1,
                  f = 852,
                  m = 592;
                function g(e) {
                  return (
                    ((e >>> 24) & 255) +
                    ((e >>> 8) & 65280) +
                    ((65280 & e) << 8) +
                    ((255 & e) << 24)
                  );
                }
                function p() {
                  ((this.mode = 0),
                    (this.last = !1),
                    (this.wrap = 0),
                    (this.havedict = !1),
                    (this.flags = 0),
                    (this.dmax = 0),
                    (this.check = 0),
                    (this.total = 0),
                    (this.head = null),
                    (this.wbits = 0),
                    (this.wsize = 0),
                    (this.whave = 0),
                    (this.wnext = 0),
                    (this.window = null),
                    (this.hold = 0),
                    (this.bits = 0),
                    (this.length = 0),
                    (this.offset = 0),
                    (this.extra = 0),
                    (this.lencode = null),
                    (this.distcode = null),
                    (this.lenbits = 0),
                    (this.distbits = 0),
                    (this.ncode = 0),
                    (this.nlen = 0),
                    (this.ndist = 0),
                    (this.have = 0),
                    (this.next = null),
                    (this.lens = new n.Buf16(320)),
                    (this.work = new n.Buf16(288)),
                    (this.lendyn = null),
                    (this.distdyn = null),
                    (this.sane = 0),
                    (this.back = 0),
                    (this.was = 0));
                }
                function y(e) {
                  var t;
                  return e && e.state
                    ? ((t = e.state),
                      (e.total_in = e.total_out = t.total = 0),
                      (e.msg = ""),
                      t.wrap && (e.adler = 1 & t.wrap),
                      (t.mode = h),
                      (t.last = 0),
                      (t.havedict = 0),
                      (t.dmax = 32768),
                      (t.head = null),
                      (t.hold = 0),
                      (t.bits = 0),
                      (t.lencode = t.lendyn = new n.Buf32(f)),
                      (t.distcode = t.distdyn = new n.Buf32(m)),
                      (t.sane = 1),
                      (t.back = -1),
                      c)
                    : u;
                }
                function b(e) {
                  var t;
                  return e && e.state
                    ? (((t = e.state).wsize = 0),
                      (t.whave = 0),
                      (t.wnext = 0),
                      y(e))
                    : u;
                }
                function w(e, t) {
                  var r, n;
                  return e && e.state
                    ? ((n = e.state),
                      t < 0
                        ? ((r = 0), (t = -t))
                        : ((r = 1 + (t >> 4)), t < 48 && (t &= 15)),
                      t && (t < 8 || 15 < t)
                        ? u
                        : (null !== n.window &&
                            n.wbits !== t &&
                            (n.window = null),
                          (n.wrap = r),
                          (n.wbits = t),
                          b(e)))
                    : u;
                }
                function v(e, t) {
                  var r, n;
                  return e
                    ? ((n = new p()),
                      ((e.state = n).window = null),
                      (r = w(e, t)) !== c && (e.state = null),
                      r)
                    : u;
                }
                var A,
                  _,
                  x = !0;
                function k(e) {
                  if (x) {
                    var t;
                    for (
                      A = new n.Buf32(512), _ = new n.Buf32(32), t = 0;
                      t < 144;

                    )
                      e.lens[t++] = 8;
                    for (; t < 256; ) e.lens[t++] = 9;
                    for (; t < 280; ) e.lens[t++] = 7;
                    for (; t < 288; ) e.lens[t++] = 8;
                    for (
                      a(l, e.lens, 0, 288, A, 0, e.work, { bits: 9 }), t = 0;
                      t < 32;

                    )
                      e.lens[t++] = 5;
                    (a(d, e.lens, 0, 32, _, 0, e.work, { bits: 5 }), (x = !1));
                  }
                  ((e.lencode = A),
                    (e.lenbits = 9),
                    (e.distcode = _),
                    (e.distbits = 5));
                }
                function S(e, t, r, i) {
                  var s,
                    o = e.state;
                  return (
                    null === o.window &&
                      ((o.wsize = 1 << o.wbits),
                      (o.wnext = 0),
                      (o.whave = 0),
                      (o.window = new n.Buf8(o.wsize))),
                    i >= o.wsize
                      ? (n.arraySet(o.window, t, r - o.wsize, o.wsize, 0),
                        (o.wnext = 0),
                        (o.whave = o.wsize))
                      : (i < (s = o.wsize - o.wnext) && (s = i),
                        n.arraySet(o.window, t, r - i, s, o.wnext),
                        (i -= s)
                          ? (n.arraySet(o.window, t, r - i, i, 0),
                            (o.wnext = i),
                            (o.whave = o.wsize))
                          : ((o.wnext += s),
                            o.wnext === o.wsize && (o.wnext = 0),
                            o.whave < o.wsize && (o.whave += s))),
                    0
                  );
                }
                ((r.inflateReset = b),
                  (r.inflateReset2 = w),
                  (r.inflateResetKeep = y),
                  (r.inflateInit = function (e) {
                    return v(e, 15);
                  }),
                  (r.inflateInit2 = v),
                  (r.inflate = function (e, t) {
                    var r,
                      f,
                      m,
                      p,
                      y,
                      b,
                      w,
                      v,
                      A,
                      _,
                      x,
                      C,
                      E,
                      O,
                      I,
                      T,
                      B,
                      D,
                      z,
                      P,
                      F,
                      L,
                      U,
                      R,
                      N = 0,
                      M = new n.Buf8(4),
                      j = [
                        16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2,
                        14, 1, 15,
                      ];
                    if (
                      !e ||
                      !e.state ||
                      !e.output ||
                      (!e.input && 0 !== e.avail_in)
                    )
                      return u;
                    (12 === (r = e.state).mode && (r.mode = 13),
                      (y = e.next_out),
                      (m = e.output),
                      (w = e.avail_out),
                      (p = e.next_in),
                      (f = e.input),
                      (b = e.avail_in),
                      (v = r.hold),
                      (A = r.bits),
                      (_ = b),
                      (x = w),
                      (L = c));
                    e: for (;;)
                      switch (r.mode) {
                        case h:
                          if (0 === r.wrap) {
                            r.mode = 13;
                            break;
                          }
                          for (; A < 16; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if (2 & r.wrap && 35615 === v) {
                            ((M[(r.check = 0)] = 255 & v),
                              (M[1] = (v >>> 8) & 255),
                              (r.check = s(r.check, M, 2, 0)),
                              (A = v = 0),
                              (r.mode = 2));
                            break;
                          }
                          if (
                            ((r.flags = 0),
                            r.head && (r.head.done = !1),
                            !(1 & r.wrap) || (((255 & v) << 8) + (v >> 8)) % 31)
                          ) {
                            ((e.msg = "incorrect header check"), (r.mode = 30));
                            break;
                          }
                          if (8 != (15 & v)) {
                            ((e.msg = "unknown compression method"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((A -= 4),
                            (F = 8 + (15 & (v >>>= 4))),
                            0 === r.wbits)
                          )
                            r.wbits = F;
                          else if (F > r.wbits) {
                            ((e.msg = "invalid window size"), (r.mode = 30));
                            break;
                          }
                          ((r.dmax = 1 << F),
                            (e.adler = r.check = 1),
                            (r.mode = 512 & v ? 10 : 12),
                            (A = v = 0));
                          break;
                        case 2:
                          for (; A < 16; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if (((r.flags = v), 8 != (255 & r.flags))) {
                            ((e.msg = "unknown compression method"),
                              (r.mode = 30));
                            break;
                          }
                          if (57344 & r.flags) {
                            ((e.msg = "unknown header flags set"),
                              (r.mode = 30));
                            break;
                          }
                          (r.head && (r.head.text = (v >> 8) & 1),
                            512 & r.flags &&
                              ((M[0] = 255 & v),
                              (M[1] = (v >>> 8) & 255),
                              (r.check = s(r.check, M, 2, 0))),
                            (A = v = 0),
                            (r.mode = 3));
                        case 3:
                          for (; A < 32; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          (r.head && (r.head.time = v),
                            512 & r.flags &&
                              ((M[0] = 255 & v),
                              (M[1] = (v >>> 8) & 255),
                              (M[2] = (v >>> 16) & 255),
                              (M[3] = (v >>> 24) & 255),
                              (r.check = s(r.check, M, 4, 0))),
                            (A = v = 0),
                            (r.mode = 4));
                        case 4:
                          for (; A < 16; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          (r.head &&
                            ((r.head.xflags = 255 & v), (r.head.os = v >> 8)),
                            512 & r.flags &&
                              ((M[0] = 255 & v),
                              (M[1] = (v >>> 8) & 255),
                              (r.check = s(r.check, M, 2, 0))),
                            (A = v = 0),
                            (r.mode = 5));
                        case 5:
                          if (1024 & r.flags) {
                            for (; A < 16; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((r.length = v),
                              r.head && (r.head.extra_len = v),
                              512 & r.flags &&
                                ((M[0] = 255 & v),
                                (M[1] = (v >>> 8) & 255),
                                (r.check = s(r.check, M, 2, 0))),
                              (A = v = 0));
                          } else r.head && (r.head.extra = null);
                          r.mode = 6;
                        case 6:
                          if (
                            1024 & r.flags &&
                            (b < (C = r.length) && (C = b),
                            C &&
                              (r.head &&
                                ((F = r.head.extra_len - r.length),
                                r.head.extra ||
                                  (r.head.extra = new Array(r.head.extra_len)),
                                n.arraySet(r.head.extra, f, p, C, F)),
                              512 & r.flags && (r.check = s(r.check, f, C, p)),
                              (b -= C),
                              (p += C),
                              (r.length -= C)),
                            r.length)
                          )
                            break e;
                          ((r.length = 0), (r.mode = 7));
                        case 7:
                          if (2048 & r.flags) {
                            if (0 === b) break e;
                            for (
                              C = 0;
                              (F = f[p + C++]),
                                r.head &&
                                  F &&
                                  r.length < 65536 &&
                                  (r.head.name += String.fromCharCode(F)),
                                F && C < b;

                            );
                            if (
                              (512 & r.flags && (r.check = s(r.check, f, C, p)),
                              (b -= C),
                              (p += C),
                              F)
                            )
                              break e;
                          } else r.head && (r.head.name = null);
                          ((r.length = 0), (r.mode = 8));
                        case 8:
                          if (4096 & r.flags) {
                            if (0 === b) break e;
                            for (
                              C = 0;
                              (F = f[p + C++]),
                                r.head &&
                                  F &&
                                  r.length < 65536 &&
                                  (r.head.comment += String.fromCharCode(F)),
                                F && C < b;

                            );
                            if (
                              (512 & r.flags && (r.check = s(r.check, f, C, p)),
                              (b -= C),
                              (p += C),
                              F)
                            )
                              break e;
                          } else r.head && (r.head.comment = null);
                          r.mode = 9;
                        case 9:
                          if (512 & r.flags) {
                            for (; A < 16; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            if (v !== (65535 & r.check)) {
                              ((e.msg = "header crc mismatch"), (r.mode = 30));
                              break;
                            }
                            A = v = 0;
                          }
                          (r.head &&
                            ((r.head.hcrc = (r.flags >> 9) & 1),
                            (r.head.done = !0)),
                            (e.adler = r.check = 0),
                            (r.mode = 12));
                          break;
                        case 10:
                          for (; A < 32; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          ((e.adler = r.check = g(v)),
                            (A = v = 0),
                            (r.mode = 11));
                        case 11:
                          if (0 === r.havedict)
                            return (
                              (e.next_out = y),
                              (e.avail_out = w),
                              (e.next_in = p),
                              (e.avail_in = b),
                              (r.hold = v),
                              (r.bits = A),
                              2
                            );
                          ((e.adler = r.check = 1), (r.mode = 12));
                        case 12:
                          if (5 === t || 6 === t) break e;
                        case 13:
                          if (r.last) {
                            ((v >>>= 7 & A), (A -= 7 & A), (r.mode = 27));
                            break;
                          }
                          for (; A < 3; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          switch (
                            ((r.last = 1 & v), (A -= 1), 3 & (v >>>= 1))
                          ) {
                            case 0:
                              r.mode = 14;
                              break;
                            case 1:
                              if ((k(r), (r.mode = 20), 6 !== t)) break;
                              ((v >>>= 2), (A -= 2));
                              break e;
                            case 2:
                              r.mode = 17;
                              break;
                            case 3:
                              ((e.msg = "invalid block type"), (r.mode = 30));
                          }
                          ((v >>>= 2), (A -= 2));
                          break;
                        case 14:
                          for (v >>>= 7 & A, A -= 7 & A; A < 32; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if ((65535 & v) != ((v >>> 16) ^ 65535)) {
                            ((e.msg = "invalid stored block lengths"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.length = 65535 & v),
                            (A = v = 0),
                            (r.mode = 15),
                            6 === t)
                          )
                            break e;
                        case 15:
                          r.mode = 16;
                        case 16:
                          if ((C = r.length)) {
                            if ((b < C && (C = b), w < C && (C = w), 0 === C))
                              break e;
                            (n.arraySet(m, f, p, C, y),
                              (b -= C),
                              (p += C),
                              (w -= C),
                              (y += C),
                              (r.length -= C));
                            break;
                          }
                          r.mode = 12;
                          break;
                        case 17:
                          for (; A < 14; ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if (
                            ((r.nlen = 257 + (31 & v)),
                            (v >>>= 5),
                            (A -= 5),
                            (r.ndist = 1 + (31 & v)),
                            (v >>>= 5),
                            (A -= 5),
                            (r.ncode = 4 + (15 & v)),
                            (v >>>= 4),
                            (A -= 4),
                            286 < r.nlen || 30 < r.ndist)
                          ) {
                            ((e.msg = "too many length or distance symbols"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.have = 0), (r.mode = 18));
                        case 18:
                          for (; r.have < r.ncode; ) {
                            for (; A < 3; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((r.lens[j[r.have++]] = 7 & v),
                              (v >>>= 3),
                              (A -= 3));
                          }
                          for (; r.have < 19; ) r.lens[j[r.have++]] = 0;
                          if (
                            ((r.lencode = r.lendyn),
                            (r.lenbits = 7),
                            (U = { bits: r.lenbits }),
                            (L = a(0, r.lens, 0, 19, r.lencode, 0, r.work, U)),
                            (r.lenbits = U.bits),
                            L)
                          ) {
                            ((e.msg = "invalid code lengths set"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.have = 0), (r.mode = 19));
                        case 19:
                          for (; r.have < r.nlen + r.ndist; ) {
                            for (
                              ;
                              (T =
                                ((N = r.lencode[v & ((1 << r.lenbits) - 1)]) >>>
                                  16) &
                                255),
                                (B = 65535 & N),
                                !((I = N >>> 24) <= A);

                            ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            if (B < 16)
                              ((v >>>= I), (A -= I), (r.lens[r.have++] = B));
                            else {
                              if (16 === B) {
                                for (R = I + 2; A < R; ) {
                                  if (0 === b) break e;
                                  (b--, (v += f[p++] << A), (A += 8));
                                }
                                if (((v >>>= I), (A -= I), 0 === r.have)) {
                                  ((e.msg = "invalid bit length repeat"),
                                    (r.mode = 30));
                                  break;
                                }
                                ((F = r.lens[r.have - 1]),
                                  (C = 3 + (3 & v)),
                                  (v >>>= 2),
                                  (A -= 2));
                              } else if (17 === B) {
                                for (R = I + 3; A < R; ) {
                                  if (0 === b) break e;
                                  (b--, (v += f[p++] << A), (A += 8));
                                }
                                ((A -= I),
                                  (F = 0),
                                  (C = 3 + (7 & (v >>>= I))),
                                  (v >>>= 3),
                                  (A -= 3));
                              } else {
                                for (R = I + 7; A < R; ) {
                                  if (0 === b) break e;
                                  (b--, (v += f[p++] << A), (A += 8));
                                }
                                ((A -= I),
                                  (F = 0),
                                  (C = 11 + (127 & (v >>>= I))),
                                  (v >>>= 7),
                                  (A -= 7));
                              }
                              if (r.have + C > r.nlen + r.ndist) {
                                ((e.msg = "invalid bit length repeat"),
                                  (r.mode = 30));
                                break;
                              }
                              for (; C--; ) r.lens[r.have++] = F;
                            }
                          }
                          if (30 === r.mode) break;
                          if (0 === r.lens[256]) {
                            ((e.msg = "invalid code -- missing end-of-block"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.lenbits = 9),
                            (U = { bits: r.lenbits }),
                            (L = a(
                              l,
                              r.lens,
                              0,
                              r.nlen,
                              r.lencode,
                              0,
                              r.work,
                              U,
                            )),
                            (r.lenbits = U.bits),
                            L)
                          ) {
                            ((e.msg = "invalid literal/lengths set"),
                              (r.mode = 30));
                            break;
                          }
                          if (
                            ((r.distbits = 6),
                            (r.distcode = r.distdyn),
                            (U = { bits: r.distbits }),
                            (L = a(
                              d,
                              r.lens,
                              r.nlen,
                              r.ndist,
                              r.distcode,
                              0,
                              r.work,
                              U,
                            )),
                            (r.distbits = U.bits),
                            L)
                          ) {
                            ((e.msg = "invalid distances set"), (r.mode = 30));
                            break;
                          }
                          if (((r.mode = 20), 6 === t)) break e;
                        case 20:
                          r.mode = 21;
                        case 21:
                          if (6 <= b && 258 <= w) {
                            ((e.next_out = y),
                              (e.avail_out = w),
                              (e.next_in = p),
                              (e.avail_in = b),
                              (r.hold = v),
                              (r.bits = A),
                              o(e, x),
                              (y = e.next_out),
                              (m = e.output),
                              (w = e.avail_out),
                              (p = e.next_in),
                              (f = e.input),
                              (b = e.avail_in),
                              (v = r.hold),
                              (A = r.bits),
                              12 === r.mode && (r.back = -1));
                            break;
                          }
                          for (
                            r.back = 0;
                            (T =
                              ((N = r.lencode[v & ((1 << r.lenbits) - 1)]) >>>
                                16) &
                              255),
                              (B = 65535 & N),
                              !((I = N >>> 24) <= A);

                          ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if (T && !(240 & T)) {
                            for (
                              D = I, z = T, P = B;
                              (T =
                                ((N =
                                  r.lencode[
                                    P + ((v & ((1 << (D + z)) - 1)) >> D)
                                  ]) >>>
                                  16) &
                                255),
                                (B = 65535 & N),
                                !(D + (I = N >>> 24) <= A);

                            ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((v >>>= D), (A -= D), (r.back += D));
                          }
                          if (
                            ((v >>>= I),
                            (A -= I),
                            (r.back += I),
                            (r.length = B),
                            0 === T)
                          ) {
                            r.mode = 26;
                            break;
                          }
                          if (32 & T) {
                            ((r.back = -1), (r.mode = 12));
                            break;
                          }
                          if (64 & T) {
                            ((e.msg = "invalid literal/length code"),
                              (r.mode = 30));
                            break;
                          }
                          ((r.extra = 15 & T), (r.mode = 22));
                        case 22:
                          if (r.extra) {
                            for (R = r.extra; A < R; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((r.length += v & ((1 << r.extra) - 1)),
                              (v >>>= r.extra),
                              (A -= r.extra),
                              (r.back += r.extra));
                          }
                          ((r.was = r.length), (r.mode = 23));
                        case 23:
                          for (
                            ;
                            (T =
                              ((N = r.distcode[v & ((1 << r.distbits) - 1)]) >>>
                                16) &
                              255),
                              (B = 65535 & N),
                              !((I = N >>> 24) <= A);

                          ) {
                            if (0 === b) break e;
                            (b--, (v += f[p++] << A), (A += 8));
                          }
                          if (!(240 & T)) {
                            for (
                              D = I, z = T, P = B;
                              (T =
                                ((N =
                                  r.distcode[
                                    P + ((v & ((1 << (D + z)) - 1)) >> D)
                                  ]) >>>
                                  16) &
                                255),
                                (B = 65535 & N),
                                !(D + (I = N >>> 24) <= A);

                            ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((v >>>= D), (A -= D), (r.back += D));
                          }
                          if (((v >>>= I), (A -= I), (r.back += I), 64 & T)) {
                            ((e.msg = "invalid distance code"), (r.mode = 30));
                            break;
                          }
                          ((r.offset = B), (r.extra = 15 & T), (r.mode = 24));
                        case 24:
                          if (r.extra) {
                            for (R = r.extra; A < R; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            ((r.offset += v & ((1 << r.extra) - 1)),
                              (v >>>= r.extra),
                              (A -= r.extra),
                              (r.back += r.extra));
                          }
                          if (r.offset > r.dmax) {
                            ((e.msg = "invalid distance too far back"),
                              (r.mode = 30));
                            break;
                          }
                          r.mode = 25;
                        case 25:
                          if (0 === w) break e;
                          if (((C = x - w), r.offset > C)) {
                            if ((C = r.offset - C) > r.whave && r.sane) {
                              ((e.msg = "invalid distance too far back"),
                                (r.mode = 30));
                              break;
                            }
                            ((E =
                              C > r.wnext
                                ? ((C -= r.wnext), r.wsize - C)
                                : r.wnext - C),
                              C > r.length && (C = r.length),
                              (O = r.window));
                          } else ((O = m), (E = y - r.offset), (C = r.length));
                          for (
                            w < C && (C = w), w -= C, r.length -= C;
                            (m[y++] = O[E++]), --C;

                          );
                          0 === r.length && (r.mode = 21);
                          break;
                        case 26:
                          if (0 === w) break e;
                          ((m[y++] = r.length), w--, (r.mode = 21));
                          break;
                        case 27:
                          if (r.wrap) {
                            for (; A < 32; ) {
                              if (0 === b) break e;
                              (b--, (v |= f[p++] << A), (A += 8));
                            }
                            if (
                              ((x -= w),
                              (e.total_out += x),
                              (r.total += x),
                              x &&
                                (e.adler = r.check =
                                  r.flags
                                    ? s(r.check, m, x, y - x)
                                    : i(r.check, m, x, y - x)),
                              (x = w),
                              (r.flags ? v : g(v)) !== r.check)
                            ) {
                              ((e.msg = "incorrect data check"), (r.mode = 30));
                              break;
                            }
                            A = v = 0;
                          }
                          r.mode = 28;
                        case 28:
                          if (r.wrap && r.flags) {
                            for (; A < 32; ) {
                              if (0 === b) break e;
                              (b--, (v += f[p++] << A), (A += 8));
                            }
                            if (v !== (4294967295 & r.total)) {
                              ((e.msg = "incorrect length check"),
                                (r.mode = 30));
                              break;
                            }
                            A = v = 0;
                          }
                          r.mode = 29;
                        case 29:
                          L = 1;
                          break e;
                        case 30:
                          L = -3;
                          break e;
                        case 31:
                          return -4;
                        default:
                          return u;
                      }
                    return (
                      (e.next_out = y),
                      (e.avail_out = w),
                      (e.next_in = p),
                      (e.avail_in = b),
                      (r.hold = v),
                      (r.bits = A),
                      (r.wsize ||
                        (x !== e.avail_out &&
                          r.mode < 30 &&
                          (r.mode < 27 || 4 !== t))) &&
                      S(e, e.output, e.next_out, x - e.avail_out)
                        ? ((r.mode = 31), -4)
                        : ((_ -= e.avail_in),
                          (x -= e.avail_out),
                          (e.total_in += _),
                          (e.total_out += x),
                          (r.total += x),
                          r.wrap &&
                            x &&
                            (e.adler = r.check =
                              r.flags
                                ? s(r.check, m, x, e.next_out - x)
                                : i(r.check, m, x, e.next_out - x)),
                          (e.data_type =
                            r.bits +
                            (r.last ? 64 : 0) +
                            (12 === r.mode ? 128 : 0) +
                            (20 === r.mode || 15 === r.mode ? 256 : 0)),
                          ((0 == _ && 0 === x) || 4 === t) &&
                            L === c &&
                            (L = -5),
                          L)
                    );
                  }),
                  (r.inflateEnd = function (e) {
                    if (!e || !e.state) return u;
                    var t = e.state;
                    return (t.window && (t.window = null), (e.state = null), c);
                  }),
                  (r.inflateGetHeader = function (e, t) {
                    var r;
                    return e && e.state && 2 & (r = e.state).wrap
                      ? (((r.head = t).done = !1), c)
                      : u;
                  }),
                  (r.inflateSetDictionary = function (e, t) {
                    var r,
                      n = t.length;
                    return e && e.state
                      ? 0 !== (r = e.state).wrap && 11 !== r.mode
                        ? u
                        : 11 === r.mode && i(1, t, n, 0) !== r.check
                          ? -3
                          : S(e, t, n, n)
                            ? ((r.mode = 31), -4)
                            : ((r.havedict = 1), c)
                      : u;
                  }),
                  (r.inflateInfo = "pako inflate (from Nodeca project)"));
              },
              {
                "../utils/common": 41,
                "./adler32": 43,
                "./crc32": 45,
                "./inffast": 48,
                "./inftrees": 50,
              },
            ],
            50: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = [
                    3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35,
                    43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0,
                  ],
                  s = [
                    16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18,
                    18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72,
                    78,
                  ],
                  o = [
                    1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,
                    257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,
                    8193, 12289, 16385, 24577, 0, 0,
                  ],
                  a = [
                    16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22,
                    22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29,
                    64, 64,
                  ];
                t.exports = function (e, t, r, l, d, c, u, h) {
                  var f,
                    m,
                    g,
                    p,
                    y,
                    b,
                    w,
                    v,
                    A,
                    _ = h.bits,
                    x = 0,
                    k = 0,
                    S = 0,
                    C = 0,
                    E = 0,
                    O = 0,
                    I = 0,
                    T = 0,
                    B = 0,
                    D = 0,
                    z = null,
                    P = 0,
                    F = new n.Buf16(16),
                    L = new n.Buf16(16),
                    U = null,
                    R = 0;
                  for (x = 0; x <= 15; x++) F[x] = 0;
                  for (k = 0; k < l; k++) F[t[r + k]]++;
                  for (E = _, C = 15; 1 <= C && 0 === F[C]; C--);
                  if ((C < E && (E = C), 0 === C))
                    return (
                      (d[c++] = 20971520),
                      (d[c++] = 20971520),
                      (h.bits = 1),
                      0
                    );
                  for (S = 1; S < C && 0 === F[S]; S++);
                  for (E < S && (E = S), x = T = 1; x <= 15; x++)
                    if (((T <<= 1), (T -= F[x]) < 0)) return -1;
                  if (0 < T && (0 === e || 1 !== C)) return -1;
                  for (L[1] = 0, x = 1; x < 15; x++) L[x + 1] = L[x] + F[x];
                  for (k = 0; k < l; k++)
                    0 !== t[r + k] && (u[L[t[r + k]]++] = k);
                  if (
                    ((b =
                      0 === e
                        ? ((z = U = u), 19)
                        : 1 === e
                          ? ((z = i), (P -= 257), (U = s), (R -= 257), 256)
                          : ((z = o), (U = a), -1)),
                    (x = S),
                    (y = c),
                    (I = k = D = 0),
                    (g = -1),
                    (p = (B = 1 << (O = E)) - 1),
                    (1 === e && 852 < B) || (2 === e && 592 < B))
                  )
                    return 1;
                  for (;;) {
                    for (
                      w = x - I,
                        A =
                          u[k] < b
                            ? ((v = 0), u[k])
                            : u[k] > b
                              ? ((v = U[R + u[k]]), z[P + u[k]])
                              : ((v = 96), 0),
                        f = 1 << (x - I),
                        S = m = 1 << O;
                      (d[y + (D >> I) + (m -= f)] = (w << 24) | (v << 16) | A),
                        0 !== m;

                    );
                    for (f = 1 << (x - 1); D & f; ) f >>= 1;
                    if (
                      (0 !== f ? ((D &= f - 1), (D += f)) : (D = 0),
                      k++,
                      0 == --F[x])
                    ) {
                      if (x === C) break;
                      x = t[r + u[k]];
                    }
                    if (E < x && (D & p) !== g) {
                      for (
                        0 === I && (I = E), y += S, T = 1 << (O = x - I);
                        O + I < C && !((T -= F[O + I]) <= 0);

                      )
                        (O++, (T <<= 1));
                      if (
                        ((B += 1 << O),
                        (1 === e && 852 < B) || (2 === e && 592 < B))
                      )
                        return 1;
                      d[(g = D & p)] = (E << 24) | (O << 16) | (y - c);
                    }
                  }
                  return (
                    0 !== D && (d[y + D] = ((x - I) << 24) | (64 << 16)),
                    (h.bits = E),
                    0
                  );
                };
              },
              { "../utils/common": 41 },
            ],
            51: [
              function (e, t, r) {
                "use strict";
                t.exports = {
                  2: "need dictionary",
                  1: "stream end",
                  0: "",
                  "-1": "file error",
                  "-2": "stream error",
                  "-3": "data error",
                  "-4": "insufficient memory",
                  "-5": "buffer error",
                  "-6": "incompatible version",
                };
              },
              {},
            ],
            52: [
              function (e, t, r) {
                "use strict";
                var n = e("../utils/common"),
                  i = 0,
                  s = 1;
                function o(e) {
                  for (var t = e.length; 0 <= --t; ) e[t] = 0;
                }
                var a = 0,
                  l = 29,
                  d = 256,
                  c = d + 1 + l,
                  u = 30,
                  h = 19,
                  f = 2 * c + 1,
                  m = 15,
                  g = 16,
                  p = 7,
                  y = 256,
                  b = 16,
                  w = 17,
                  v = 18,
                  A = [
                    0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3,
                    4, 4, 4, 4, 5, 5, 5, 5, 0,
                  ],
                  _ = [
                    0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8,
                    9, 9, 10, 10, 11, 11, 12, 12, 13, 13,
                  ],
                  x = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7],
                  k = [
                    16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14,
                    1, 15,
                  ],
                  S = new Array(2 * (c + 2));
                o(S);
                var C = new Array(2 * u);
                o(C);
                var E = new Array(512);
                o(E);
                var O = new Array(256);
                o(O);
                var I = new Array(l);
                o(I);
                var T,
                  B,
                  D,
                  z = new Array(u);
                function P(e, t, r, n, i) {
                  ((this.static_tree = e),
                    (this.extra_bits = t),
                    (this.extra_base = r),
                    (this.elems = n),
                    (this.max_length = i),
                    (this.has_stree = e && e.length));
                }
                function F(e, t) {
                  ((this.dyn_tree = e),
                    (this.max_code = 0),
                    (this.stat_desc = t));
                }
                function L(e) {
                  return e < 256 ? E[e] : E[256 + (e >>> 7)];
                }
                function U(e, t) {
                  ((e.pending_buf[e.pending++] = 255 & t),
                    (e.pending_buf[e.pending++] = (t >>> 8) & 255));
                }
                function R(e, t, r) {
                  e.bi_valid > g - r
                    ? ((e.bi_buf |= (t << e.bi_valid) & 65535),
                      U(e, e.bi_buf),
                      (e.bi_buf = t >> (g - e.bi_valid)),
                      (e.bi_valid += r - g))
                    : ((e.bi_buf |= (t << e.bi_valid) & 65535),
                      (e.bi_valid += r));
                }
                function N(e, t, r) {
                  R(e, r[2 * t], r[2 * t + 1]);
                }
                function M(e, t) {
                  for (
                    var r = 0;
                    (r |= 1 & e), (e >>>= 1), (r <<= 1), 0 < --t;

                  );
                  return r >>> 1;
                }
                function j(e, t, r) {
                  var n,
                    i,
                    s = new Array(m + 1),
                    o = 0;
                  for (n = 1; n <= m; n++) s[n] = o = (o + r[n - 1]) << 1;
                  for (i = 0; i <= t; i++) {
                    var a = e[2 * i + 1];
                    0 !== a && (e[2 * i] = M(s[a]++, a));
                  }
                }
                function W(e) {
                  var t;
                  for (t = 0; t < c; t++) e.dyn_ltree[2 * t] = 0;
                  for (t = 0; t < u; t++) e.dyn_dtree[2 * t] = 0;
                  for (t = 0; t < h; t++) e.bl_tree[2 * t] = 0;
                  ((e.dyn_ltree[2 * y] = 1),
                    (e.opt_len = e.static_len = 0),
                    (e.last_lit = e.matches = 0));
                }
                function $(e) {
                  (8 < e.bi_valid
                    ? U(e, e.bi_buf)
                    : 0 < e.bi_valid && (e.pending_buf[e.pending++] = e.bi_buf),
                    (e.bi_buf = 0),
                    (e.bi_valid = 0));
                }
                function Z(e, t, r, n) {
                  var i = 2 * t,
                    s = 2 * r;
                  return e[i] < e[s] || (e[i] === e[s] && n[t] <= n[r]);
                }
                function V(e, t, r) {
                  for (
                    var n = e.heap[r], i = r << 1;
                    i <= e.heap_len &&
                    (i < e.heap_len &&
                      Z(t, e.heap[i + 1], e.heap[i], e.depth) &&
                      i++,
                    !Z(t, n, e.heap[i], e.depth));

                  )
                    ((e.heap[r] = e.heap[i]), (r = i), (i <<= 1));
                  e.heap[r] = n;
                }
                function H(e, t, r) {
                  var n,
                    i,
                    s,
                    o,
                    a = 0;
                  if (0 !== e.last_lit)
                    for (
                      ;
                      (n =
                        (e.pending_buf[e.d_buf + 2 * a] << 8) |
                        e.pending_buf[e.d_buf + 2 * a + 1]),
                        (i = e.pending_buf[e.l_buf + a]),
                        a++,
                        0 === n
                          ? N(e, i, t)
                          : (N(e, (s = O[i]) + d + 1, t),
                            0 !== (o = A[s]) && R(e, (i -= I[s]), o),
                            N(e, (s = L(--n)), r),
                            0 !== (o = _[s]) && R(e, (n -= z[s]), o)),
                        a < e.last_lit;

                    );
                  N(e, y, t);
                }
                function K(e, t) {
                  var r,
                    n,
                    i,
                    s = t.dyn_tree,
                    o = t.stat_desc.static_tree,
                    a = t.stat_desc.has_stree,
                    l = t.stat_desc.elems,
                    d = -1;
                  for (e.heap_len = 0, e.heap_max = f, r = 0; r < l; r++)
                    0 !== s[2 * r]
                      ? ((e.heap[++e.heap_len] = d = r), (e.depth[r] = 0))
                      : (s[2 * r + 1] = 0);
                  for (; e.heap_len < 2; )
                    ((s[2 * (i = e.heap[++e.heap_len] = d < 2 ? ++d : 0)] = 1),
                      (e.depth[i] = 0),
                      e.opt_len--,
                      a && (e.static_len -= o[2 * i + 1]));
                  for (t.max_code = d, r = e.heap_len >> 1; 1 <= r; r--)
                    V(e, s, r);
                  for (
                    i = l;
                    (r = e.heap[1]),
                      (e.heap[1] = e.heap[e.heap_len--]),
                      V(e, s, 1),
                      (n = e.heap[1]),
                      (e.heap[--e.heap_max] = r),
                      (e.heap[--e.heap_max] = n),
                      (s[2 * i] = s[2 * r] + s[2 * n]),
                      (e.depth[i] =
                        (e.depth[r] >= e.depth[n] ? e.depth[r] : e.depth[n]) +
                        1),
                      (s[2 * r + 1] = s[2 * n + 1] = i),
                      (e.heap[1] = i++),
                      V(e, s, 1),
                      2 <= e.heap_len;

                  );
                  ((e.heap[--e.heap_max] = e.heap[1]),
                    (function (e, t) {
                      var r,
                        n,
                        i,
                        s,
                        o,
                        a,
                        l = t.dyn_tree,
                        d = t.max_code,
                        c = t.stat_desc.static_tree,
                        u = t.stat_desc.has_stree,
                        h = t.stat_desc.extra_bits,
                        g = t.stat_desc.extra_base,
                        p = t.stat_desc.max_length,
                        y = 0;
                      for (s = 0; s <= m; s++) e.bl_count[s] = 0;
                      for (
                        l[2 * e.heap[e.heap_max] + 1] = 0, r = e.heap_max + 1;
                        r < f;
                        r++
                      )
                        (p < (s = l[2 * l[2 * (n = e.heap[r]) + 1] + 1] + 1) &&
                          ((s = p), y++),
                          (l[2 * n + 1] = s),
                          d < n ||
                            (e.bl_count[s]++,
                            (o = 0),
                            g <= n && (o = h[n - g]),
                            (a = l[2 * n]),
                            (e.opt_len += a * (s + o)),
                            u && (e.static_len += a * (c[2 * n + 1] + o))));
                      if (0 !== y) {
                        do {
                          for (s = p - 1; 0 === e.bl_count[s]; ) s--;
                          (e.bl_count[s]--,
                            (e.bl_count[s + 1] += 2),
                            e.bl_count[p]--,
                            (y -= 2));
                        } while (0 < y);
                        for (s = p; 0 !== s; s--)
                          for (n = e.bl_count[s]; 0 !== n; )
                            d < (i = e.heap[--r]) ||
                              (l[2 * i + 1] !== s &&
                                ((e.opt_len += (s - l[2 * i + 1]) * l[2 * i]),
                                (l[2 * i + 1] = s)),
                              n--);
                      }
                    })(e, t),
                    j(s, d, e.bl_count));
                }
                function q(e, t, r) {
                  var n,
                    i,
                    s = -1,
                    o = t[1],
                    a = 0,
                    l = 7,
                    d = 4;
                  for (
                    0 === o && ((l = 138), (d = 3)),
                      t[2 * (r + 1) + 1] = 65535,
                      n = 0;
                    n <= r;
                    n++
                  )
                    ((i = o),
                      (o = t[2 * (n + 1) + 1]),
                      (++a < l && i === o) ||
                        (a < d
                          ? (e.bl_tree[2 * i] += a)
                          : 0 !== i
                            ? (i !== s && e.bl_tree[2 * i]++,
                              e.bl_tree[2 * b]++)
                            : a <= 10
                              ? e.bl_tree[2 * w]++
                              : e.bl_tree[2 * v]++,
                        (s = i),
                        (d =
                          (a = 0) === o
                            ? ((l = 138), 3)
                            : i === o
                              ? ((l = 6), 3)
                              : ((l = 7), 4))));
                }
                function G(e, t, r) {
                  var n,
                    i,
                    s = -1,
                    o = t[1],
                    a = 0,
                    l = 7,
                    d = 4;
                  for (0 === o && ((l = 138), (d = 3)), n = 0; n <= r; n++)
                    if (
                      ((i = o), (o = t[2 * (n + 1) + 1]), !(++a < l && i === o))
                    ) {
                      if (a < d) for (; N(e, i, e.bl_tree), 0 != --a; );
                      else
                        0 !== i
                          ? (i !== s && (N(e, i, e.bl_tree), a--),
                            N(e, b, e.bl_tree),
                            R(e, a - 3, 2))
                          : a <= 10
                            ? (N(e, w, e.bl_tree), R(e, a - 3, 3))
                            : (N(e, v, e.bl_tree), R(e, a - 11, 7));
                      ((s = i),
                        (d =
                          (a = 0) === o
                            ? ((l = 138), 3)
                            : i === o
                              ? ((l = 6), 3)
                              : ((l = 7), 4)));
                    }
                }
                o(z);
                var Y = !1;
                function Q(e, t, r, i) {
                  (R(e, (a << 1) + (i ? 1 : 0), 3),
                    (function (e, t, r, i) {
                      ($(e),
                        i && (U(e, r), U(e, ~r)),
                        n.arraySet(e.pending_buf, e.window, t, r, e.pending),
                        (e.pending += r));
                    })(e, t, r, !0));
                }
                ((r._tr_init = function (e) {
                  (Y ||
                    ((function () {
                      var e,
                        t,
                        r,
                        n,
                        i,
                        s = new Array(m + 1);
                      for (n = r = 0; n < l - 1; n++)
                        for (I[n] = r, e = 0; e < 1 << A[n]; e++) O[r++] = n;
                      for (O[r - 1] = n, n = i = 0; n < 16; n++)
                        for (z[n] = i, e = 0; e < 1 << _[n]; e++) E[i++] = n;
                      for (i >>= 7; n < u; n++)
                        for (z[n] = i << 7, e = 0; e < 1 << (_[n] - 7); e++)
                          E[256 + i++] = n;
                      for (t = 0; t <= m; t++) s[t] = 0;
                      for (e = 0; e <= 143; ) ((S[2 * e + 1] = 8), e++, s[8]++);
                      for (; e <= 255; ) ((S[2 * e + 1] = 9), e++, s[9]++);
                      for (; e <= 279; ) ((S[2 * e + 1] = 7), e++, s[7]++);
                      for (; e <= 287; ) ((S[2 * e + 1] = 8), e++, s[8]++);
                      for (j(S, c + 1, s), e = 0; e < u; e++)
                        ((C[2 * e + 1] = 5), (C[2 * e] = M(e, 5)));
                      ((T = new P(S, A, d + 1, c, m)),
                        (B = new P(C, _, 0, u, m)),
                        (D = new P(new Array(0), x, 0, h, p)));
                    })(),
                    (Y = !0)),
                    (e.l_desc = new F(e.dyn_ltree, T)),
                    (e.d_desc = new F(e.dyn_dtree, B)),
                    (e.bl_desc = new F(e.bl_tree, D)),
                    (e.bi_buf = 0),
                    (e.bi_valid = 0),
                    W(e));
                }),
                  (r._tr_stored_block = Q),
                  (r._tr_flush_block = function (e, t, r, n) {
                    var o,
                      a,
                      l = 0;
                    (0 < e.level
                      ? (2 === e.strm.data_type &&
                          (e.strm.data_type = (function (e) {
                            var t,
                              r = 4093624447;
                            for (t = 0; t <= 31; t++, r >>>= 1)
                              if (1 & r && 0 !== e.dyn_ltree[2 * t]) return i;
                            if (
                              0 !== e.dyn_ltree[18] ||
                              0 !== e.dyn_ltree[20] ||
                              0 !== e.dyn_ltree[26]
                            )
                              return s;
                            for (t = 32; t < d; t++)
                              if (0 !== e.dyn_ltree[2 * t]) return s;
                            return i;
                          })(e)),
                        K(e, e.l_desc),
                        K(e, e.d_desc),
                        (l = (function (e) {
                          var t;
                          for (
                            q(e, e.dyn_ltree, e.l_desc.max_code),
                              q(e, e.dyn_dtree, e.d_desc.max_code),
                              K(e, e.bl_desc),
                              t = h - 1;
                            3 <= t && 0 === e.bl_tree[2 * k[t] + 1];
                            t--
                          );
                          return ((e.opt_len += 3 * (t + 1) + 5 + 5 + 4), t);
                        })(e)),
                        (o = (e.opt_len + 3 + 7) >>> 3),
                        (a = (e.static_len + 3 + 7) >>> 3) <= o && (o = a))
                      : (o = a = r + 5),
                      r + 4 <= o && -1 !== t
                        ? Q(e, t, r, n)
                        : 4 === e.strategy || a === o
                          ? (R(e, 2 + (n ? 1 : 0), 3), H(e, S, C))
                          : (R(e, 4 + (n ? 1 : 0), 3),
                            (function (e, t, r, n) {
                              var i;
                              for (
                                R(e, t - 257, 5),
                                  R(e, r - 1, 5),
                                  R(e, n - 4, 4),
                                  i = 0;
                                i < n;
                                i++
                              )
                                R(e, e.bl_tree[2 * k[i] + 1], 3);
                              (G(e, e.dyn_ltree, t - 1),
                                G(e, e.dyn_dtree, r - 1));
                            })(
                              e,
                              e.l_desc.max_code + 1,
                              e.d_desc.max_code + 1,
                              l + 1,
                            ),
                            H(e, e.dyn_ltree, e.dyn_dtree)),
                      W(e),
                      n && $(e));
                  }),
                  (r._tr_tally = function (e, t, r) {
                    return (
                      (e.pending_buf[e.d_buf + 2 * e.last_lit] =
                        (t >>> 8) & 255),
                      (e.pending_buf[e.d_buf + 2 * e.last_lit + 1] = 255 & t),
                      (e.pending_buf[e.l_buf + e.last_lit] = 255 & r),
                      e.last_lit++,
                      0 === t
                        ? e.dyn_ltree[2 * r]++
                        : (e.matches++,
                          t--,
                          e.dyn_ltree[2 * (O[r] + d + 1)]++,
                          e.dyn_dtree[2 * L(t)]++),
                      e.last_lit === e.lit_bufsize - 1
                    );
                  }),
                  (r._tr_align = function (e) {
                    (R(e, 2, 3),
                      N(e, y, S),
                      (function (e) {
                        16 === e.bi_valid
                          ? (U(e, e.bi_buf), (e.bi_buf = 0), (e.bi_valid = 0))
                          : 8 <= e.bi_valid &&
                            ((e.pending_buf[e.pending++] = 255 & e.bi_buf),
                            (e.bi_buf >>= 8),
                            (e.bi_valid -= 8));
                      })(e));
                  }));
              },
              { "../utils/common": 41 },
            ],
            53: [
              function (e, t, r) {
                "use strict";
                t.exports = function () {
                  ((this.input = null),
                    (this.next_in = 0),
                    (this.avail_in = 0),
                    (this.total_in = 0),
                    (this.output = null),
                    (this.next_out = 0),
                    (this.avail_out = 0),
                    (this.total_out = 0),
                    (this.msg = ""),
                    (this.state = null),
                    (this.data_type = 2),
                    (this.adler = 0));
                };
              },
              {},
            ],
            54: [
              function (e, t, r) {
                (function (e) {
                  !(function (e, t) {
                    "use strict";
                    if (!e.setImmediate) {
                      var r,
                        n,
                        i,
                        s,
                        o = 1,
                        a = {},
                        l = !1,
                        d = e.document,
                        c = Object.getPrototypeOf && Object.getPrototypeOf(e);
                      ((c = c && c.setTimeout ? c : e),
                        (r =
                          "[object process]" === {}.toString.call(e.process)
                            ? function (e) {
                                process.nextTick(function () {
                                  h(e);
                                });
                              }
                            : (function () {
                                  if (e.postMessage && !e.importScripts) {
                                    var t = !0,
                                      r = e.onmessage;
                                    return (
                                      (e.onmessage = function () {
                                        t = !1;
                                      }),
                                      e.postMessage("", "*"),
                                      (e.onmessage = r),
                                      t
                                    );
                                  }
                                })()
                              ? ((s = "setImmediate$" + Math.random() + "$"),
                                e.addEventListener
                                  ? e.addEventListener("message", f, !1)
                                  : e.attachEvent("onmessage", f),
                                function (t) {
                                  e.postMessage(s + t, "*");
                                })
                              : e.MessageChannel
                                ? (((i = new MessageChannel()).port1.onmessage =
                                    function (e) {
                                      h(e.data);
                                    }),
                                  function (e) {
                                    i.port2.postMessage(e);
                                  })
                                : d &&
                                    "onreadystatechange" in
                                      d.createElement("script")
                                  ? ((n = d.documentElement),
                                    function (e) {
                                      var t = d.createElement("script");
                                      ((t.onreadystatechange = function () {
                                        (h(e),
                                          (t.onreadystatechange = null),
                                          n.removeChild(t),
                                          (t = null));
                                      }),
                                        n.appendChild(t));
                                    })
                                  : function (e) {
                                      setTimeout(h, 0, e);
                                    }),
                        (c.setImmediate = function (e) {
                          "function" != typeof e && (e = new Function("" + e));
                          for (
                            var t = new Array(arguments.length - 1), n = 0;
                            n < t.length;
                            n++
                          )
                            t[n] = arguments[n + 1];
                          var i = { callback: e, args: t };
                          return ((a[o] = i), r(o), o++);
                        }),
                        (c.clearImmediate = u));
                    }
                    function u(e) {
                      delete a[e];
                    }
                    function h(e) {
                      if (l) setTimeout(h, 0, e);
                      else {
                        var r = a[e];
                        if (r) {
                          l = !0;
                          try {
                            !(function (e) {
                              var r = e.callback,
                                n = e.args;
                              switch (n.length) {
                                case 0:
                                  r();
                                  break;
                                case 1:
                                  r(n[0]);
                                  break;
                                case 2:
                                  r(n[0], n[1]);
                                  break;
                                case 3:
                                  r(n[0], n[1], n[2]);
                                  break;
                                default:
                                  r.apply(t, n);
                              }
                            })(r);
                          } finally {
                            (u(e), (l = !1));
                          }
                        }
                      }
                    }
                    function f(t) {
                      t.source === e &&
                        "string" == typeof t.data &&
                        0 === t.data.indexOf(s) &&
                        h(+t.data.slice(s.length));
                    }
                  })(
                    "undefined" == typeof self
                      ? void 0 === e
                        ? this
                        : e
                      : self,
                  );
                }).call(
                  this,
                  "undefined" != typeof global
                    ? global
                    : "undefined" != typeof self
                      ? self
                      : "undefined" != typeof window
                        ? window
                        : {},
                );
              },
              {},
            ],
          },
          {},
          [10],
        )(10);
      },
      416: (e, t, r) => {
        "use strict";
        "undefined" == typeof window ? (t.browser = {}) : (t.browser = r(337));
      },
      337: function (e, t) {
        var r, n, i;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (n = [e]),
          (r = function (e) {
            "use strict";
            if (
              "undefined" == typeof browser ||
              Object.getPrototypeOf(browser) !== Object.prototype
            ) {
              const t =
                  "The message port closed before a response was received.",
                r =
                  "Returning a Promise is the preferred way to send a reply from an onMessage/onMessageExternal listener, as the sendResponse will be removed from the specs (See https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/runtime/onMessage)",
                n = (e) => {
                  const n = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(n).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class i extends WeakMap {
                    constructor(e, t = void 0) {
                      (super(t), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const s = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    o =
                      (t, r) =>
                      (...n) => {
                        e.runtime.lastError
                          ? t.reject(e.runtime.lastError)
                          : r.singleCallbackArg ||
                              (n.length <= 1 && !1 !== r.singleCallbackArg)
                            ? t.resolve(n[0])
                            : t.resolve(n);
                      },
                    a = (e) => (1 == e ? "argument" : "arguments"),
                    l = (e, t) =>
                      function (r, ...n) {
                        if (n.length < t.minArgs)
                          throw new Error(
                            `Expected at least ${t.minArgs} ${a(t.minArgs)} for ${e}(), got ${n.length}`,
                          );
                        if (n.length > t.maxArgs)
                          throw new Error(
                            `Expected at most ${t.maxArgs} ${a(t.maxArgs)} for ${e}(), got ${n.length}`,
                          );
                        return new Promise((i, s) => {
                          if (t.fallbackToNoCallback)
                            try {
                              r[e](...n, o({ resolve: i, reject: s }, t));
                            } catch (s) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                s,
                              ),
                                r[e](...n),
                                (t.fallbackToNoCallback = !1),
                                (t.noCallback = !0),
                                i());
                            }
                          else
                            t.noCallback
                              ? (r[e](...n), i())
                              : r[e](...n, o({ resolve: i, reject: s }, t));
                        });
                      },
                    d = (e, t, r) =>
                      new Proxy(t, { apply: (t, n, i) => r.call(n, e, ...i) });
                  let c = Function.call.bind(Object.prototype.hasOwnProperty);
                  const u = (e, t = {}, r = {}) => {
                      let n = Object.create(null),
                        i = {
                          has: (t, r) => r in e || r in n,
                          get(i, s, o) {
                            if (s in n) return n[s];
                            if (!(s in e)) return;
                            let a = e[s];
                            if ("function" == typeof a)
                              if ("function" == typeof t[s])
                                a = d(e, e[s], t[s]);
                              else if (c(r, s)) {
                                let t = l(s, r[s]);
                                a = d(e, e[s], t);
                              } else a = a.bind(e);
                            else if (
                              "object" == typeof a &&
                              null !== a &&
                              (c(t, s) || c(r, s))
                            )
                              a = u(a, t[s], r[s]);
                            else {
                              if (!c(r, "*"))
                                return (
                                  Object.defineProperty(n, s, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[s],
                                    set(t) {
                                      e[s] = t;
                                    },
                                  }),
                                  a
                                );
                              a = u(a, t[s], r["*"]);
                            }
                            return ((n[s] = a), a);
                          },
                          set: (t, r, i, s) => (
                            r in n ? (n[r] = i) : (e[r] = i),
                            !0
                          ),
                          defineProperty: (e, t, r) =>
                            Reflect.defineProperty(n, t, r),
                          deleteProperty: (e, t) =>
                            Reflect.deleteProperty(n, t),
                        },
                        s = Object.create(e);
                      return new Proxy(s, i);
                    },
                    h = (e) => ({
                      addListener(t, r, ...n) {
                        t.addListener(e.get(r), ...n);
                      },
                      hasListener: (t, r) => t.hasListener(e.get(r)),
                      removeListener(t, r) {
                        t.removeListener(e.get(r));
                      },
                    });
                  let f = !1;
                  const m = new i((e) =>
                      "function" != typeof e
                        ? e
                        : function (t, n, i) {
                            let o,
                              a,
                              l = !1,
                              d = new Promise((e) => {
                                o = function (t) {
                                  (f ||
                                    (console.warn(r, new Error().stack),
                                    (f = !0)),
                                    (l = !0),
                                    e(t));
                                };
                              });
                            try {
                              a = e(t, n, o);
                            } catch (e) {
                              a = Promise.reject(e);
                            }
                            const c = !0 !== a && s(a);
                            if (!0 !== a && !c && !l) return !1;
                            const u = (e) => {
                              e.then(
                                (e) => {
                                  i(e);
                                },
                                (e) => {
                                  let t;
                                  ((t =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    i({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: t,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (u(c ? a : d), !0);
                          },
                    ),
                    g = ({ reject: r, resolve: n }, i) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === t
                          ? n()
                          : r(e.runtime.lastError)
                        : i && i.__mozWebExtensionPolyfillReject__
                          ? r(new Error(i.message))
                          : n(i);
                    },
                    p = (e, t, r, ...n) => {
                      if (n.length < t.minArgs)
                        throw new Error(
                          `Expected at least ${t.minArgs} ${a(t.minArgs)} for ${e}(), got ${n.length}`,
                        );
                      if (n.length > t.maxArgs)
                        throw new Error(
                          `Expected at most ${t.maxArgs} ${a(t.maxArgs)} for ${e}(), got ${n.length}`,
                        );
                      return new Promise((e, t) => {
                        const i = g.bind(null, { resolve: e, reject: t });
                        (n.push(i), r.sendMessage(...n));
                      });
                    },
                    y = {
                      runtime: {
                        onMessage: h(m),
                        onMessageExternal: h(m),
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    b = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (n.privacy = {
                      network: { "*": b },
                      services: { "*": b },
                      websites: { "*": b },
                    }),
                    u(e, y, n)
                  );
                };
              if (
                "object" != typeof chrome ||
                !chrome ||
                !chrome.runtime ||
                !chrome.runtime.id
              )
                throw new Error(
                  "This script should only be loaded in a browser extension.",
                );
              e.exports = n(chrome);
            } else e.exports = browser;
          }),
          void 0 === (i = "function" == typeof r ? r.apply(t, n) : r) ||
            (e.exports = i));
      },
      150: function (e, t) {
        var r, n, i;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (n = [e]),
          (r = function (e) {
            "use strict";
            if (
              !(
                globalThis.chrome &&
                globalThis.chrome.runtime &&
                globalThis.chrome.runtime.id
              )
            )
              throw new Error(
                "This script should only be loaded in a browser extension.",
              );
            if (
              globalThis.browser &&
              globalThis.browser.runtime &&
              globalThis.browser.runtime.id
            )
              e.exports = globalThis.browser;
            else {
              const t =
                  "The message port closed before a response was received.",
                r = (e) => {
                  const r = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(r).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class n extends WeakMap {
                    constructor(e, t = void 0) {
                      (super(t), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const i = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    s =
                      (t, r) =>
                      (...n) => {
                        e.runtime.lastError
                          ? t.reject(new Error(e.runtime.lastError.message))
                          : r.singleCallbackArg ||
                              (n.length <= 1 && !1 !== r.singleCallbackArg)
                            ? t.resolve(n[0])
                            : t.resolve(n);
                      },
                    o = (e) => (1 == e ? "argument" : "arguments"),
                    a = (e, t) =>
                      function (r, ...n) {
                        if (n.length < t.minArgs)
                          throw new Error(
                            `Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${n.length}`,
                          );
                        if (n.length > t.maxArgs)
                          throw new Error(
                            `Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${n.length}`,
                          );
                        return new Promise((i, o) => {
                          if (t.fallbackToNoCallback)
                            try {
                              r[e](...n, s({ resolve: i, reject: o }, t));
                            } catch (s) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                s,
                              ),
                                r[e](...n),
                                (t.fallbackToNoCallback = !1),
                                (t.noCallback = !0),
                                i());
                            }
                          else
                            t.noCallback
                              ? (r[e](...n), i())
                              : r[e](...n, s({ resolve: i, reject: o }, t));
                        });
                      },
                    l = (e, t, r) =>
                      new Proxy(t, { apply: (t, n, i) => r.call(n, e, ...i) });
                  let d = Function.call.bind(Object.prototype.hasOwnProperty);
                  const c = (e, t = {}, r = {}) => {
                      let n = Object.create(null),
                        i = {
                          has: (t, r) => r in e || r in n,
                          get(i, s, o) {
                            if (s in n) return n[s];
                            if (!(s in e)) return;
                            let u = e[s];
                            if ("function" == typeof u)
                              if ("function" == typeof t[s])
                                u = l(e, e[s], t[s]);
                              else if (d(r, s)) {
                                let t = a(s, r[s]);
                                u = l(e, e[s], t);
                              } else u = u.bind(e);
                            else if (
                              "object" == typeof u &&
                              null !== u &&
                              (d(t, s) || d(r, s))
                            )
                              u = c(u, t[s], r[s]);
                            else {
                              if (!d(r, "*"))
                                return (
                                  Object.defineProperty(n, s, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[s],
                                    set(t) {
                                      e[s] = t;
                                    },
                                  }),
                                  u
                                );
                              u = c(u, t[s], r["*"]);
                            }
                            return ((n[s] = u), u);
                          },
                          set: (t, r, i, s) => (
                            r in n ? (n[r] = i) : (e[r] = i),
                            !0
                          ),
                          defineProperty: (e, t, r) =>
                            Reflect.defineProperty(n, t, r),
                          deleteProperty: (e, t) =>
                            Reflect.deleteProperty(n, t),
                        },
                        s = Object.create(e);
                      return new Proxy(s, i);
                    },
                    u = (e) => ({
                      addListener(t, r, ...n) {
                        t.addListener(e.get(r), ...n);
                      },
                      hasListener: (t, r) => t.hasListener(e.get(r)),
                      removeListener(t, r) {
                        t.removeListener(e.get(r));
                      },
                    }),
                    h = new n((e) =>
                      "function" != typeof e
                        ? e
                        : function (t) {
                            const r = c(
                              t,
                              {},
                              { getContent: { minArgs: 0, maxArgs: 0 } },
                            );
                            e(r);
                          },
                    ),
                    f = new n((e) =>
                      "function" != typeof e
                        ? e
                        : function (t, r, n) {
                            let s,
                              o,
                              a = !1,
                              l = new Promise((e) => {
                                s = function (t) {
                                  ((a = !0), e(t));
                                };
                              });
                            try {
                              o = e(t, r, s);
                            } catch (e) {
                              o = Promise.reject(e);
                            }
                            const d = !0 !== o && i(o);
                            if (!0 !== o && !d && !a) return !1;
                            const c = (e) => {
                              e.then(
                                (e) => {
                                  n(e);
                                },
                                (e) => {
                                  let t;
                                  ((t =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    n({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: t,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (c(d ? o : l), !0);
                          },
                    ),
                    m = ({ reject: r, resolve: n }, i) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === t
                          ? n()
                          : r(new Error(e.runtime.lastError.message))
                        : i && i.__mozWebExtensionPolyfillReject__
                          ? r(new Error(i.message))
                          : n(i);
                    },
                    g = (e, t, r, ...n) => {
                      if (n.length < t.minArgs)
                        throw new Error(
                          `Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${n.length}`,
                        );
                      if (n.length > t.maxArgs)
                        throw new Error(
                          `Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${n.length}`,
                        );
                      return new Promise((e, t) => {
                        const i = m.bind(null, { resolve: e, reject: t });
                        (n.push(i), r.sendMessage(...n));
                      });
                    },
                    p = {
                      devtools: { network: { onRequestFinished: u(h) } },
                      runtime: {
                        onMessage: u(f),
                        onMessageExternal: u(f),
                        sendMessage: g.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: g.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    y = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (r.privacy = {
                      network: { "*": y },
                      services: { "*": y },
                      websites: { "*": y },
                    }),
                    c(e, p, r)
                  );
                };
              e.exports = r(chrome);
            }
          }),
          void 0 === (i = "function" == typeof r ? r.apply(t, n) : r) ||
            (e.exports = i));
      },
    },
    t = {};
  function r(n) {
    var i = t[n];
    if (void 0 !== i) return i.exports;
    var s = (t[n] = { exports: {} });
    return (e[n].call(s.exports, s, s.exports, r), s.exports);
  }
  ((r.n = (e) => {
    var t = e && e.__esModule ? () => e.default : () => e;
    return (r.d(t, { a: t }), t);
  }),
    (r.d = (e, t) => {
      for (var n in t)
        r.o(t, n) &&
          !r.o(e, n) &&
          Object.defineProperty(e, n, { enumerable: !0, get: t[n] });
    }),
    (r.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t)),
    (() => {
      "use strict";
      function e(e, t, r, n) {
        var i,
          s = arguments.length,
          o =
            s < 3
              ? t
              : null === n
                ? (n = Object.getOwnPropertyDescriptor(t, r))
                : n;
        if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
          o = Reflect.decorate(e, t, r, n);
        else
          for (var a = e.length - 1; a >= 0; a--)
            (i = e[a]) &&
              (o = (s < 3 ? i(o) : s > 3 ? i(t, r, o) : i(t, r)) || o);
        return (s > 3 && o && Object.defineProperty(t, r, o), o);
      }
      function t(e, t, r, n) {
        return new (r || (r = Promise))(function (i, s) {
          function o(e) {
            try {
              l(n.next(e));
            } catch (e) {
              s(e);
            }
          }
          function a(e) {
            try {
              l(n.throw(e));
            } catch (e) {
              s(e);
            }
          }
          function l(e) {
            var t;
            e.done
              ? i(e.value)
              : ((t = e.value),
                t instanceof r
                  ? t
                  : new r(function (e) {
                      e(t);
                    })).then(o, a);
          }
          l((n = n.apply(e, t || [])).next());
        });
      }
      Object.create;
      Object.create;
      var n = r(150),
        i = r.n(n);
      class s {
        constructor() {
          this.subscribers = [];
        }
        subscribe(e) {
          return (
            this.subscribers.includes(e) || this.subscribers.push(e),
            new a(e, this.subscribers)
          );
        }
        emit(e = null) {
          for (const t of this.subscribers) t(e);
        }
      }
      class o {
        constructor() {
          this.subscribers = {};
        }
        on(e, t) {
          e in this.subscribers || (this.subscribers[e] = []);
          const r = this.subscribers[e];
          return (r.includes(t) || r.push(t), new a(t, this.subscribers[e]));
        }
        emit(e, t = null) {
          if (e in this.subscribers) for (const r of this.subscribers[e]) r(t);
        }
      }
      class a {
        constructor(e, t) {
          ((this.callback = e), (this.subscribers = t));
        }
        unsubscribe() {
          this.subscribers.includes(this.callback) &&
            this.subscribers.splice(
              this.subscribers.findIndex((e) => e === this.callback),
              1,
            );
        }
      }
      let l = class extends s {
        constructor() {
          (super(),
            (this.timeout = null),
            (this.mutationObserver = new MutationObserver(
              this.changeCallback.bind(this),
            )));
        }
        disconnect() {
          this.mutationObserver.disconnect();
        }
        observe() {
          this.mutationObserver.observe(document.body, {
            childList: !0,
            subtree: !0,
          });
        }
        takeRecords() {
          return this.mutationObserver.takeRecords();
        }
        changeCallback() {
          (this.timeout && clearTimeout(this.timeout),
            (this.timeout = setTimeout(() => {
              this.emit(null);
            }, 100)));
        }
      };
      var d;
      l = e([N], l);
      let c = (d = class {
        constructor() {
          this.subscription = { unsubscribe: () => null };
        }
        init() {
          ((this.subscription = d.observer.subscribe(
            this.reinitialize.bind(this),
          )),
            this.createDownloadButton());
        }
        remove(e) {
          this.subscription.unsubscribe();
          Array.from(document.querySelectorAll(e)).forEach((e) => {
            try {
              e.remove();
            } catch (e) {}
          });
        }
        getAccountName(e, t) {
          let r;
          try {
            r = e.querySelector(t).innerText;
          } catch (e) {
            r = "no_account_found";
          }
          return r;
        }
      });
      var u, h, f, m, g, p, y, b, w, v, A, _;
      ((c.observer = new l()),
        e([M], c.prototype, "init", null),
        e([M], c.prototype, "remove", null),
        (c = d = e([j], c)),
        (function (e) {
          ((e[(e.pre = 0)] = "pre"),
            (e[(e.after = 1)] = "after"),
            (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
        })(u || (u = {})),
        (function (e) {
          ((e[(e.single = 0)] = "single"),
            (e[(e.bulk = 1)] = "bulk"),
            (e[(e.bloburl = 2)] = "bloburl"),
            (e[(e.changeUrl = 3)] = "changeUrl"),
            (e[(e.login = 4)] = "login"),
            (e[(e.googleLogin = 5)] = "googleLogin"),
            (e[(e.register = 6)] = "register"),
            (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
            (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
            (e[(e.getConfig = 9)] = "getConfig"),
            (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
            (e[(e.updateNoPerDayDownloadCount = 11)] =
              "updateNoPerDayDownloadCount"));
        })(h || (h = {})),
        (function (e) {
          ((e[(e.goSubscribe = 0)] = "goSubscribe"),
            (e[(e.pureNotice = 1)] = "pureNotice"),
            (e[(e.drmLicense = 2)] = "drmLicense"),
            (e[(e.retryMessage = 3)] = "retryMessage"),
            (e[(e.serverError = 4)] = "serverError"));
        })(f || (f = {})),
        (function (e) {
          ((e[(e.Edge = 0)] = "Edge"),
            (e[(e.Chrome = 1)] = "Chrome"),
            (e[(e.Firefox = 2)] = "Firefox"),
            (e[(e.Opera = 3)] = "Opera"),
            (e[(e.Safari = 4)] = "Safari"),
            (e[(e.Unknown = 5)] = "Unknown"));
        })(m || (m = {})),
        (function (e) {
          ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
        })(g || (g = {})),
        (function (e) {
          ((e.install = "install"),
            (e.uninstall = "uninstall"),
            (e.downloadSignalUnkown = "downloadSignalUnkown"),
            (e.downloadSignalImg = "downloadSignalImg"),
            (e.downloadSignalVideo = "downloadSignalVideo"),
            (e.downloadBulk = "downloadBulk"),
            (e.changeUrl = "changeUrl"),
            (e.register = "register"),
            (e.login = "login"),
            (e.googleLogin = "googleLogin"),
            (e.sendEmailCode = "sendEmailCode"),
            (e.uploadFiles = "uploadFiles"),
            (e.concatVideoAndAudio = "concatVideoAndAudio"));
        })(p || (p = {})),
        (function (e) {
          ((e.downloadSuccess = "downloadSuccess"),
            (e.downloadError = "downloadError"),
            (e.downloadCancle = "downloadCancle"),
            (e.downloadWating = "downloadWating"),
            (e.downloadPrepare = "downloadPrepare"),
            (e.downloadStuck = "downloadStuck"));
        })(y || (y = {})),
        (function (e) {
          ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
            (e.updateDownloadStatus = "updateDownloadStatus"));
        })(b || (b = {})),
        (function (e) {
          e[(e.refresh = 0)] = "refresh";
        })(w || (w = {})),
        (function (e) {
          ((e.downloading = "downloading"),
            (e.downloaded = "downloaded"),
            (e.download = "download"),
            (e.all = "all"),
            (e.quota = "quota"));
        })(v || (v = {})),
        (function (e) {
          ((e.processVideo = "processVideo"),
            (e.processVideoInWeb = "processVideoInWeb"),
            (e.processVideoByUrl = "processVideoByUrl"));
        })(A || (A = {})),
        (function (e) {
          ((e.serverError = "serverError"), (e.tip = "tip"));
        })(_ || (_ = {})));
      var x;
      !(function (e) {
        let r;
        const n = () => {
          var e;
          null === (e = document.getElementById("addOnInfoWrapperid")) ||
            void 0 === e ||
            e.remove();
          const t = document.createElement("div");
          return (
            (t.id = "addOnInfoWrapperid"),
            (t.innerHTML =
              '\n    <div class="modal" id="modal">\n        <div class="modal-header">\n            <div id="addon-info-title"></div>\n        </div>\n        <div class="modal-content">\n        </div>\n        <div class="modal-footer">            \n        </div>\n    </div>\n    '),
            document.body.appendChild(t),
            t
          );
        };
        function s(e, t, r) {
          const n = document.createElement("button");
          return (
            n.classList.add("btn", e),
            (n.textContent = t),
            n.addEventListener("click", function () {
              (null != r && r(), a());
            }),
            n
          );
        }
        function o() {
          const e = document.getElementById("modal");
          ((e.style.display = "block"),
            setTimeout(() => {
              e.classList.add("show");
            }, 10));
        }
        function a() {
          const e = document.getElementById("modal");
          (e.classList.remove("show"),
            setTimeout(() => {
              ((e.style.display = "none"), clearInterval(r));
            }, 300));
        }
        ((e.displayMessage = function (t, r = 10) {
          L(location.href) &&
            (t.type == f.goSubscribe
              ? (console.log(t),
                console.log(t.mainAction),
                t.mainAction && "blank" == t.mainAction
                  ? (console.log(111),
                    e.openModalWithButton(
                      t.title,
                      t.text,
                      t.mainText,
                      () => {
                        window.open(t.mainUrl, "_blank");
                      },
                      t.subText,
                      null,
                    ))
                  : e.openModalWithButton(
                      t.title,
                      t.text,
                      t.mainText,
                      () => {
                        window.location.href = t.mainUrl;
                      },
                      t.subText,
                      null,
                    ))
              : t.type == f.pureNotice &&
                e.openModalWithTimer(t.title, t.text, r));
        }),
          (e.openModalWithTimer = function (e, i, s = 10) {
            if (L(location.href)) {
              n();
              const l = document.getElementById("addon-info-title"),
                d = document.querySelector(".modal-content"),
                c = document.querySelector(".modal-footer");
              (clearInterval(r),
                (d.innerHTML = i),
                (c.innerHTML = ""),
                (l.innerHTML = e));
              const u = document.createElement("div");
              ((u.id = "countdown"),
                (u.textContent = `close in ${s}s`),
                c.appendChild(u));
              let h = s;
              r = setInterval(() => {
                h <= 0
                  ? (clearInterval(r), a())
                  : ((u.textContent = `close in ${h}s`), h--);
              }, 1e3);
              const f = document.querySelector(".noticButtonP .noticA");
              (f &&
                (f.onclick = () =>
                  t(this, void 0, void 0, function* () {
                    let e = yield D("discordUrl");
                    (window.open(e + "", "_blank"),
                      yield z("isJumpDiscord", !0));
                  })),
                o());
            }
          }),
          (e.openModalWithButton = function (e, t, a, l, d, c) {
            if (L(location.href)) {
              n();
              const u = document.getElementById("addon-info-title"),
                h = document.querySelector(".modal-content"),
                f = document.querySelector(".modal-footer");
              if (
                (clearInterval(r),
                (h.innerHTML = t),
                (f.innerHTML = ""),
                (u.innerHTML = e),
                null != a)
              ) {
                const e = s("btn-wishlist", a, l);
                f.appendChild(e);
              }
              if (null != d) {
                const e = s("btn-no-thanks", d, c);
                f.appendChild(e);
              }
              const m = document.querySelector(".notice_openSiderpanle");
              (m &&
                (m.onclick = () => {
                  let e = i().runtime.connect({ name: "openSidepanels" });
                  (e.postMessage({}), e.disconnect());
                }),
                o());
            }
          }));
      })(x || (x = {}));
      var k = (function () {
        var e = "undefined" != typeof self ? self : this,
          t = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          r = {
            createUUID: function () {
              for (var e = [], t = "0123456789abcdef", r = 0; r < 36; r++)
                e[r] = t.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = t.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                t = e.getFullYear(),
                r = e.getMonth() + 1,
                n = e.getDate(),
                i = e.getHours(),
                s = e.getMinutes(),
                o = e.getSeconds();
              return ""
                .concat(t.toString(), "/")
                .concat(r.toString(), "/")
                .concat(n.toString(), " ")
                .concat(i.toString(), ":")
                .concat(s.toString(), ":")
                .concat(o.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var n = t.navigator.userAgent || {},
                i = r.getMatchMap(n);
              for (var s in t.infoMap)
                for (var o = 0; o < t.infoMap[s].length; o++) {
                  var a = t.infoMap[s][o];
                  i[a] && (e[s] = a);
                }
            },
            getOS: function () {
              return (r.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                r = t.navigator.userAgent || {};
              e.osVersion = "";
              var n = {
                Windows: function () {
                  var e = r.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return r.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return r
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return r.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return r.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return r
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return r.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                n[e.os] &&
                  ((e.osVersion = n[e.os]()),
                  e.osVersion == r && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), r.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    t.navigator.browserLanguage || t.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              r.matchInfoMap(e);
              var n = t.navigator.userAgent || {},
                i = r.getMatchMap(n);
              if (
                (i.Baidu && i.Opera && (i.Baidu = !1),
                i.Mobile && (i.Mobile = !(n.indexOf("iPad") > -1)),
                i.IE || i.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    i["360EE"] = !0;
                    break;
                  case 104:
                    i["360SE"] = !0;
                }
              var s = {
                Safari: function () {
                  return n.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return n
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return n
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return n.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return n
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return n.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return n.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return n
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return n.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return n.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return n.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return n.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return n.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return n.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return n.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return n.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return n.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return n.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return n.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return n.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return n.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                s[e.browser] &&
                  ((e.browserVersion = s[e.browser]()),
                  e.browserVersion == n && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  n.match(/\S+Browser/) &&
                  ((e.browser = n.match(/\S+Browser/)[0]),
                  (e.version = n.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (i.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(s.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, t) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (t) {
                        e(t);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : t("fail");
              });
            },
            getPlatform: function () {
              return (
                (t.navigator.userAgentData &&
                  t.navigator.userAgentData.platform) ||
                t.navigator.platform
              );
            },
          },
          n = {
            DeviceInfoObj: function (e) {
              var n = {
                  deviceType: r.getDeviceType(),
                  os: r.getOS(),
                  osVersion: r.getOSVersion(),
                  platform: r.getPlatform(),
                  language: r.getLanguage(),
                  network: r.getNetwork(),
                  browserInfo: r.getBrowserInfo(),
                  userAgent: t.navigator.userAgent,
                  geoPosition: !0,
                  date: r.getDate(),
                  timezoneOffset: r.getTimezoneOffset(),
                  timezone: r.getTimezone(),
                  uuid: r.createUUID(),
                },
                i = {};
              if (e && e.info && 0 !== e.info.length) {
                var s = {},
                  o = function (t) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === t.toLowerCase() &&
                        (s[(e = t)] = n[e]);
                    });
                  };
                for (var a in n) o(a);
                i = s;
              } else i = n;
              return i;
            },
          };
        return {
          Info: function (e) {
            return n.DeviceInfoObj(e);
          },
        };
      })();
      function S() {
        return k.Info({
          info: [
            "deviceType",
            "OS",
            "OSVersion",
            "platform",
            "language",
            "netWork",
            "browserInfo",
            "screenHeight",
            "screenWidth",
            "userAgent",
            "appCodeName",
            "appName",
            "appVersion",
            "geoPosition",
            "date",
            "UUID",
            "timezoneOffset",
            "timezone",
          ],
        });
      }
      class C {
        static userReg(e, t, r, n, i, s) {
          const o = new FormData();
          let a = S();
          for (const e in a) o.append(e, a[e]);
          (o.append("userId", e),
            o.append("extId", t),
            o.append("version", r),
            o.append("action", n),
            o.append("detail", JSON.stringify(i)),
            console.log(
              "fetch url:https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
            ),
            fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
              { method: "POST", body: o },
            ).then((e) => {
              s && s(e);
            }));
        }
        static logAction(e, r, n, i, s, o, a) {
          return t(this, void 0, void 0, function* () {
            console.log("====start log verion:" + n);
            const t = new FormData();
            let l = S();
            for (const e in l) t.append(e, l[e]);
            (t.append("userId", e),
              t.append("extId", r),
              t.append("version", n),
              t.append("action", i),
              t.append("detail", JSON.stringify(s)),
              o &&
                (t.append("url", o), t.append("domain", new URL(o).hostname)));
            let d = null;
            try {
              let e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/log-action-1",
                { method: "POST", body: t },
              );
              (a && a(e), (d = e.json()));
            } catch (e) {}
            return d;
          });
        }
        static userReg2(e, r, n, i, s, o, a, l) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            let d = S();
            for (const e in d) t.append(e, d[e]);
            (t.append("userId", e),
              t.append("extId", r),
              t.append("version", n),
              t.append("action", i),
              t.append("email", s),
              t.append("password", o),
              t.append("emailCode", a),
              t.append("detail", JSON.stringify(l)));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-2",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static userLogin(e, r, n, i, s, o, a) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            let l = S();
            for (const e in l) t.append(e, l[e]);
            (t.append("userId", e),
              t.append("extId", r),
              t.append("version", n),
              t.append("action", i),
              t.append("email", s),
              t.append("password", o),
              t.append("detail", JSON.stringify(a)));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/login",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static sendEmailCode(e, r, n, i, s, o) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            let a = S();
            for (const e in a) t.append(e, a[e]);
            (t.append("userId", e),
              t.append("extId", r),
              t.append("version", n),
              t.append("action", i),
              t.append("email", s),
              t.append("detail", JSON.stringify(o)));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/getEmailCode",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyPre(e) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            t.append("pssh", e);
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              const r = yield e.json();
              return 0 == r.code
                ? { success: !0, data: r.data }
                : { success: !1, error: r.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyAfter(e, r, n, i) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            (t.append("session_id", e), t.append("licence", r));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyAfter",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              const r = yield e.json();
              return 0 == r.code
                ? (yield this.saveDrmKey(
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    n,
                    r.data.keys.trim(),
                    i,
                  ),
                  { success: !0, data: r.data })
                : { success: !1, error: r.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static checkHasVideo(e, r) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            (t.append("extId", e), t.append("courseId", r));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/file/ude/checkHasVideo",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getExtDrmKey(e, r) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            (t.append("extId", e), t.append("courseId", r));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getExtDrmKey",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static saveDrmKey(e, r, n, i) {
          return t(this, void 0, void 0, function* () {
            const t = new FormData();
            (t.append("extId", e),
              t.append("courseId", r),
              t.append("drmKey", n),
              t.append("userId", i));
            try {
              const e = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/saveDrmKey",
                { method: "POST", body: t },
              );
              if (!e.ok) {
                return { success: !1, error: yield e.json() };
              }
              return { success: !0, data: yield e.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getConfig() {
          return t(this, void 0, void 0, function* () {
            let e = "",
              t = !0;
            try {
              const r = new FormData();
              r.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af");
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getConfig",
                { method: "POST", body: r },
              );
              if (n.ok) {
                e = (yield n.json()).data;
              } else t = !1;
            } catch (e) {
              ((t = !1), console.log(e));
            }
            return (
              !t && this.retryCount < this.retryMaxCount
                ? (this.retryCount++, yield T(1e3), this.getConfig())
                : (this.retryCount = 0),
              e
            );
          });
        }
        static getUserMemberInfo(e, r) {
          return t(this, void 0, void 0, function* () {
            let t = "";
            try {
              const n = new FormData();
              let i = S();
              for (const e in i) n.append(e, i[e]);
              (n.append("userId", e),
                n.append("version", r),
                n.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const s = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/getUserMemberInfoNew",
                { method: "POST", body: n },
              );
              if (s.ok) {
                t = (yield s.json()).data;
              }
            } catch (e) {
              console.log(e);
            }
            return t;
          });
        }
        static getDownloadCount(e) {
          return t(this, void 0, void 0, function* () {
            let t = null;
            try {
              const r = new FormData();
              let n = S();
              for (const e in n) r.append(e, n[e]);
              (r.append("userId", e),
                r.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const i = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getDownloadCount",
                { method: "POST", body: r },
              );
              if (i.ok) {
                const e = yield i.json();
                0 === e.code && (t = e.data);
              }
            } catch (e) {
              console.log(e);
            }
            return t;
          });
        }
        static updateNoPerDayDownloadCount(e) {
          return t(this, void 0, void 0, function* () {
            try {
              const t = new FormData();
              let r = S();
              for (const e in r) t.append(e, r[e]);
              (t.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                t.append("detail", e));
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/updateNoPerDayDownloadCount",
                { method: "POST", body: t },
              );
              if (n.ok) {
                0 === (yield n.json()).code &&
                  console.log("update downloadCount success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
        static downloadRecord(e, r) {
          return t(this, void 0, void 0, function* () {
            try {
              const t = new FormData();
              let n = S();
              for (const e in n) t.append(e, n[e]);
              (t.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                t.append("userId", e),
                t.append("version", i().runtime.getManifest().version));
              let s = JSON.stringify({ perday: r });
              t.append("detail", s);
              const o = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/record/ude/downloadRecord",
                { method: "POST", body: t },
              );
              if (o.ok) {
                0 === (yield o.json()).code &&
                  console.log("downloadRecord success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
      }
      ((C.retryCount = 0), (C.retryMaxCount = 3));
      const E = "udemyDownloadingInfo",
        O = "udemyDownloadInfo",
        I = "udemyDownloadedInfo";
      function T(e) {
        return new Promise((t) => setTimeout(t, e));
      }
      function B() {
        return t(this, void 0, void 0, function* () {
          let e = yield D("UdemyDownloaderUserInfo");
          return null != e && null != e.userId && "" != e.userId ? e : null;
        });
      }
      function D(e) {
        return i()
          .storage.local.get(e)
          .then((t) => t[e]);
      }
      function z(e, t) {
        return i().storage.local.set({ [e]: t });
      }
      function P(e) {
        return t(this, void 0, void 0, function* () {
          let r = !1,
            n = !1,
            s = "count",
            o = 0,
            a = yield (function () {
              return t(this, void 0, void 0, function* () {
                let e = !1,
                  t = (yield B()).canBusiness,
                  r =
                    ((n = location.href),
                    !"www.udemy.com,downloadsp.html,service_work.js,service_work_firefox.js"
                      .split(",")
                      .some((e) => n.includes(e)));
                var n;
                return ("1" != t && r && (e = !0), e);
              });
            })(),
            l = yield (function () {
              return t(this, void 0, void 0, function* () {
                let e = parseInt("2");
                try {
                  let e = {
                    videoUrlList: [],
                    videoNameList: [],
                    type: h.getMemberInfo,
                  };
                  yield i().runtime.sendMessage(e);
                } catch (e) {
                } finally {
                  let t = yield B(),
                    r = null == t.maxDownloadCount ? 0 : t.maxDownloadCount,
                    n = t.noPerDayMemberList,
                    i = 0;
                  if (null != n && n.length > 0)
                    for (let e = 0; e < n.length; e++) {
                      let t = yield D(n[e].id + ""),
                        r = null == t ? 0 : t,
                        s =
                          null == n[e].downloadedCount
                            ? 0
                            : n[e].downloadedCount;
                      s > r && ((r = s), yield z(n[e].id + "", r));
                      let o = n[e].downloadCount - r;
                      ((o = o < 0 ? 0 : o), (i += o));
                    }
                  e = r + i;
                }
                return e;
              });
            })(),
            d = yield D("downloadCount");
          if (-2 == d) ((r = !0), (n = !0));
          else {
            let t = yield B(),
              n = t.maxDownloadCount < 0 ? 0 : t.maxDownloadCount;
            ((d = d < 0 ? 0 : d),
              d > t.maxDownloadCount && (d = n),
              console.log(d, l));
            let i = (yield D(E)) || [],
              c = (yield D(O)) || [];
            (a || d + i.length + c.length + e >= l) &&
              ((r = !0),
              a
                ? ((s = "business"), (o = 0))
                : (o = l - (d + i.length + c.length)));
          }
          return {
            isNeedLogin: r,
            type: s,
            canDownloadCount: o,
            serverIsError: n,
          };
        });
      }
      new (class {
        constructor() {
          this.locks = {};
        }
        acquire(e, r) {
          return t(this, void 0, void 0, function* () {
            const t = (this.locks[e] || Promise.resolve())
              .then(r)
              .catch(() => {});
            return ((this.locks[e] = t), t);
          });
        }
      })();
      function F() {
        return t(this, void 0, void 0, function* () {
          // Discord message removed - local activation
          return "";
        });
      }
      function L(e) {
        return "udemy.com,udemybusiness,ssudemy.com,udemyfreecourses.org,discudemy.com,premiumm.click,freecourseudemy.com"
          .split(",")
          .some((t) => e.includes(t));
      }
      function U(e) {
        return t(this, void 0, void 0, function* () {
          let t = yield B(),
            r = t.allDownloadedCount;
          r < t.maxDownloadCount && (r = t.maxDownloadCount);
          let n = (yield D(O)) || [],
            s = (yield D(E)) || [];
          const o = ((yield D(I)) || []).filter(
            (e) => e.status == y.downloadSuccess,
          );
          // Download message removed - local activation with unlimited downloads
          let c = {
            title: "Download MORE",
            text: d,
            mainText: "Yes, download more",
            mainAction: "blank",
            mainUrl: l,
            subText: "No, I do not",
            subUrl: null,
            type: f.goSubscribe,
          };
          x.displayMessage(c);
        });
      }
      function R() {
        return t(this, void 0, void 0, function* () {
          let e = {
            title: "UdemyFetcher™",
            text: "Server exception! Please try again later",
            mainText: "",
            mainUrl: "",
            subText: "",
            subUrl: "",
            type: f.pureNotice,
          };
          x.displayMessage(e);
        });
      }
      function N(e) {
        return new Proxy(e, {
          construct: (e, t, r) =>
            e.prototype !== r.prototype
              ? Reflect.construct(e, t, r)
              : (e.SINGLETON_INSTANCE ||
                  (e.SINGLETON_INSTANCE = Reflect.construct(e, t, r)),
                e.SINGLETON_INSTANCE),
        });
      }
      function M(e, t, r) {
        const n = r.value;
        r.value = function () {
          (c.observer.disconnect(),
            c.observer.takeRecords(),
            T(100).then(() => n.apply(this, arguments)),
            T(150).then(() => c.observer.observe()));
        };
      }
      function j(e) {
        const t = {};
        for (const r of Object.getOwnPropertyNames(e.prototype)) {
          const n = Object.getOwnPropertyDescriptor(e.prototype, r),
            i = n.value;
          i instanceof Function &&
            "constructor" !== r &&
            ((n.value = function (...r) {
              try {
                return i.apply(this, r);
              } catch (r) {
                if ("Error: Extension context invalidated." === r.toString())
                  return;
                const n = encodeURIComponent(`${e.name} ${r.toString()}`);
                if (t[n]) return;
                t[n] = !0;
              }
            }),
            Object.defineProperty(e.prototype, r, n));
        }
      }
      var W;
      !(function (e) {
        ((e.leftArea = ".app--content-column--LnPGp"),
          (e.playArea = ".shaka-control-bar--control-bar--gXZ1u"),
          (e.courseTakeing = "ud-component--course-taking--app"),
          (e.singleCourseInMyStudy = ".shaka-control-bar--spacer--xEX10"),
          (e.postDownloadingBtn = "span.post-saving-btn"),
          (e.postDownloadBtn = "span.post-save-btn"),
          (e.postDownloadingWhiteBtn = "span.udemy-post-saving-btn-white"),
          (e.postDownloadWhiteBtn = "span.udemy-post-save-btn-white"),
          (e.selectedVedio =
            '.curriculum-item-link--curriculum-item--OVP5S.curriculum-item-link--is-current--2mKk4 span[data-purpose="item-title"]'),
          (e.rightTop = ".sidebar--sidebar-header--Ohywj"),
          (e.rightCloseBtn =
            ".popper-module--popper--mM5Ie.sidebar--tooltip--mlocI"),
          (e.courseTitle = ".header--header-text--zBvgT"),
          (e.siderBtn = ".udemy-sider"),
          (e.tabBtn = ".carousel-module--scroll-port--ViaiR"));
      })(W || (W = {}));
      var $ = r(416);
      class Z {
        static getCookies() {
          return $.browser.cookies.getAll({ url: this.webUrl });
        }
        static waitFor(e) {
          return t(this, void 0, void 0, function* () {
            new Promise((t) => setTimeout(t, e));
          });
        }
        static asyncForEach(e, r) {
          return t(this, void 0, void 0, function* () {
            yield Promise.all(e.map((t, n) => r(t, n, e)));
          });
        }
        static getAllVedios(e) {
          return t(this, void 0, void 0, function* () {
            let r = new Array(),
              n = (yield Z.getPlayList(e)).results.filter(
                (e) => void 0 !== e.asset && "Video" === e.asset.asset_type,
              );
            return (
              yield Z.asyncForEach(n, (n) =>
                t(this, void 0, void 0, function* () {
                  yield Z.waitFor(0);
                  const t = yield Z.getSingleVideo(e, n.id);
                  let i = "";
                  try {
                    i = t.asset.stream_urls.Video[0].file;
                  } catch (e) {}
                  let s = "";
                  t.asset.media_sources &&
                    t.asset.media_sources.length > 0 &&
                    (s = t.asset.media_sources[0].src);
                  let o = this.getCaptionUrl(t.asset);
                  r.push({
                    id: n.id,
                    url: i,
                    title: `${n.object_index}. ${n.title}`,
                    isDrmed: t.asset.course_is_drmed,
                    mediaUrl: s,
                    captionUrl: o,
                  });
                }),
              ),
              { videoArr: r }
            );
          });
        }
        static getPlayList(e) {
          return t(this, void 0, void 0, function* () {
            const t = {
                url:
                  this.webUrl +
                  "/api-2.0/courses/" +
                  e +
                  "/subscriber-curriculum-items",
                type: "GET",
                data: {
                  page_size: "1500",
                  "fields[lecture]":
                    "title,object_index,is_published,sort_order,created,asset,supplementary_assets,is_free",
                  "fields[quiz]":
                    "title,object_index,is_published,sort_order,type",
                  "fields[practice]":
                    "title,object_index,is_published,sort_order",
                  "fields[chapter]":
                    "title,object_index,is_published,sort_order",
                  "fields[asset]":
                    "title,filename,asset_type,status,time_estimation,is_external",
                  caching_intent: "True",
                },
              },
              r = new URL(t.url);
            Object.keys(t.data).forEach((e) =>
              r.searchParams.append(e, t.data[e]),
            );
            try {
              const e = yield fetch(r, {
                method: t.type,
                headers: { "Content-Type": "application/json" },
                credentials: "include",
              });
              if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
              return yield e.json();
            } catch (e) {
              throw (console.log("Error:", e), e);
            }
          });
        }
        static getAllCourseList(e) {
          return t(this, void 0, void 0, function* () {
            const t = {
                url:
                  this.webUrl +
                  "/api-2.0/courses/" +
                  e +
                  "/subscriber-curriculum-items",
                type: "GET",
                data: {
                  page_size: "1500",
                  "fields[lecture]":
                    "title,object_index,is_published,sort_order,created,asset,supplementary_assets,is_free",
                  "fields[quiz]":
                    "title,object_index,is_published,sort_order,type",
                  "fields[practice]":
                    "title,object_index,is_published,sort_order",
                  "fields[chapter]":
                    "title,object_index,is_published,sort_order",
                  "fields[asset]":
                    "title,filename,asset_type,media_license_token,course_is_drmed,status,time_estimation,is_external",
                  caching_intent: "True",
                },
              },
              r = new URL(t.url);
            Object.keys(t.data).forEach((e) =>
              r.searchParams.append(e, t.data[e]),
            );
            try {
              const e = yield fetch(r, {
                method: t.type,
                headers: { "Content-Type": "application/json" },
                credentials: "include",
              });
              if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
              return yield e.json();
            } catch (e) {
              throw (console.log("Error:", e), e);
            }
          });
        }
        static getSingleVideo(e, r) {
          return t(this, void 0, void 0, function* () {
            const t = new URL(
                this.webUrl +
                  `/api-2.0/users/me/subscribed-courses/${e}/lectures/${r}`,
              ),
              n = {
                "fields[lecture]":
                  "asset,description,download_url,is_free,last_watched_second",
                "fields[asset]":
                  "asset_type,length,media_license_token,course_is_drmed,media_sources,stream_urls,captions,thumbnail_sprite,slides,slide_urls,download_urls,image_125_H",
              };
            Object.keys(n).forEach((e) => t.searchParams.append(e, n[e]));
            try {
              const e = yield fetch(t, {
                method: "GET",
                headers: { "Content-Type": "application/json" },
                credentials: "include",
              });
              if (!e.ok) throw new Error(`HTTP error! status: ${e.status}`);
              return yield e.json();
            } catch (e) {
              throw (console.log("Error fetching video details:", e), e);
            }
          });
        }
        static fetchDrmSecretkey(e) {
          return t(this, void 0, void 0, function* () {
            return yield fetch(window.udemyFetcher.licenseUrl, {
              method: "POST",
              headers: { "Content-Type": "application/octet-stream" },
              credentials: "include",
              body: Uint8Array.from(atob(e), (e) => e.charCodeAt(0)),
            })
              .then((e) => e.arrayBuffer())
              .then((e) => btoa(String.fromCharCode(...new Uint8Array(e))));
          });
        }
        static getDrmSecretKey(e) {
          return t(this, void 0, void 0, function* () {
            let t = yield D(e.toString());
            if (!t) {
              let r = {
                  type: h.getDrmSecretKey,
                  courseId: e,
                  drmMessage: { pssh: "", stage: u.getExtDrmKey },
                },
                n = yield $.browser.runtime.sendMessage(r);
              if (
                n.success &&
                n.data &&
                0 == n.data.code &&
                n.data.data &&
                n.data.data.drmKey
              )
                ((t = n.data.data.drmKey), yield z(e.toString(), t));
              else if (window.udemyFetcher.PSSH) {
                let r = {
                    type: h.getDrmSecretKey,
                    courseId: e,
                    drmMessage: {
                      pssh: window.udemyFetcher.PSSH,
                      stage: u.pre,
                    },
                  },
                  n = yield $.browser.runtime.sendMessage(r);
                if (n.success) {
                  let r = n.data.session_id,
                    i = n.data.challenge,
                    s = yield this.fetchDrmSecretkey(i),
                    o = {
                      type: h.getDrmSecretKey,
                      courseId: e,
                      drmMessage: {
                        pssh: window.udemyFetcher.PSSH,
                        session_id: r,
                        content: s,
                        stage: u.after,
                      },
                    },
                    a = yield $.browser.runtime.sendMessage(o);
                  a.success &&
                    ((t = a.data.keys.trim()), yield z(e.toString(), t));
                }
              }
            }
            return t;
          });
        }
        static getElementsDataModuleArgs(e) {
          const t = document.getElementsByClassName(e);
          return Array.from(t).map((e) => {
            const t = e.getAttribute("data-module-args");
            try {
              return JSON.parse(t);
            } catch (e) {
              return (console.log("Error parsing data-module-args:", e), null);
            }
          });
        }
        static getCaptionUrl(e) {
          let t = "";
          try {
            if (e.captions) {
              let r = "";
              const n = document.querySelectorAll(
                ".control-bar-dropdown--menu--o7N0r [role='menuitemradio'][aria-checked='true'] .ud-block-list-item-content",
              );
              if (n && n.length > 0) {
                for (let i = 0; i < n.length; i++) {
                  r = n[i].textContent;
                  const s = e.captions.find((e) => e.video_label === r);
                  if (((t = s ? s.url : ""), t && "" != t)) break;
                }
                "" == t && (t = e.captions[0].url);
              }
            }
          } catch (e) {}
          return t;
        }
        static injetIframe(e) {
          return t(this, void 0, void 0, function* () {
            Z.removeIframe();
            let t = document.createElement("iframe");
            ((t.id = "udemyDynamicIframe"),
              (t.style.position = "fixed"),
              (t.style.width = "80%"),
              (t.style.height = "80%"),
              (t.style.top = "10%"),
              (t.style.left = "10%"),
              (t.style.zIndex = "10000"),
              (t.style.border = "none"),
              (t.style.backgroundColor = "white"),
              (t.style.display = "none"),
              (t.src = e),
              document.body.appendChild(t));
            const r = document.createElement("script");
            r.src = $.browser.runtime.getURL("../assets/libs/play_inject.js");
            let n = 0,
              i = !1;
            n < 3 &&
              !i &&
              (t.contentWindow &&
              t.contentWindow.document &&
              t.contentWindow.document.head
                ? (t.contentWindow.document.head.appendChild(r), (i = !0))
                : yield T(20),
              n++);
          });
        }
        static removeIframe() {
          let e = document.getElementById("udemyDynamicIframe");
          e && (e.remove(), console.log("close iframe..."));
        }
      }
      Z.webUrl = "https://" + window.location.hostname;
      var V,
        H = r(733);
      let K = (V = class extends c {
        constructor() {
          (super(...arguments),
            (this.creationTimeoutList = []),
            (this.removed = !0));
        }
        static downloadSigle(e) {
          return t(this, void 0, void 0, function* () {
            // CIH99 Protection Check
            if (!CIH99_CONTENT_GUARD.check()) {
              console.warn('CIH99: Download blocked - system not ready');
              return false;
            }
            let t = !1,
              { isNeedLogin: r, type: n, serverIsError: i } = yield P(0);
            if (r) (i ? yield R() : yield U(n), this.hiddenButton(e, h.single));
            else {
              let r = Z.getElementsDataModuleArgs(W.courseTakeing)[0].courseId,
                n = this.extractLectureId(location.href),
                i = yield Z.getSingleVideo(r, n);
              t = yield this.download(r, e, i, h.single);
            }
            return t;
          });
        }
        static downloadMuti(e) {
          return t(this, void 0, void 0, function* () {
            // CIH99 Protection Check
            if (!CIH99_CONTENT_GUARD.check()) {
              console.warn('CIH99: Bulk download blocked - system not ready');
              return false;
            }
            let r = !1;
            try {
              let n = Z.getElementsDataModuleArgs(W.courseTakeing)[0].courseId,
                i = yield Z.getAllVedios(n);
              if (!i.videoArr) throw new Error("can not get video url.");
              {
                let s = yield V.filterDownloadedVideo(i.videoArr),
                  o = i.videoArr.length - s.length;
                if (o > 0) {
                  let e = {
                    title: "Bulk download notice",
                    text:
                      "You have already downloaded " +
                      o +
                      " videos for this course before. These videos will not be downloaded again this time to save your download limit.",
                    mainText: "",
                    mainUrl: "",
                    subText: "",
                    subUrl: "",
                    type: f.pureNotice,
                  };
                  x.displayMessage(e);
                }
                if (
                  ((i.videoArr = s),
                  console.log(i.videoArr),
                  i.videoArr.length > 0)
                ) {
                  let {
                    isNeedLogin: s,
                    type: o,
                    canDownloadCount: a,
                    serverIsError: l,
                  } = yield P(i.videoArr.length);
                  s
                    ? (l
                        ? yield R()
                        : (console.log(a),
                          a > 0
                            ? ((i.videoArr =
                                i.videoArr.length >= a
                                  ? i.videoArr.slice(0, a)
                                  : i.videoArr.slice(0)),
                              (r = yield this.download(n, e, i, h.bulk)),
                              setTimeout(
                                () =>
                                  t(this, void 0, void 0, function* () {
                                    yield U(o);
                                  }),
                                3e3,
                              ))
                            : yield U(o)),
                      this.hiddenButton(e, h.bulk))
                    : (r = yield this.download(n, e, i, h.bulk));
                } else this.hiddenButton(e, h.bulk);
              }
            } catch (t) {
              this.hiddenButton(e, h.bulk);
              let r = {
                title: "UdemyFetcher™",
                text: "download fail：" + t.toString() + (yield F()),
                mainText: "",
                mainUrl: "",
                subText: "",
                subUrl: "",
                type: f.pureNotice,
              };
              x.displayMessage(r);
            }
            return r;
          });
        }
        static download(e, r, n, s) {
          return t(this, void 0, void 0, function* () {
            let t = !0,
              o = "",
              a = !1;
            try {
              let r = Z.webUrl;
              (window.udemyFetcher || (window.udemyFetcher = {}),
                window.udemyFetcher.secretKey ||
                  (window.udemyFetcher.secretKey = yield Z.getDrmSecretKey(e)));
              let l = { videoUrlList: [], videoNameList: [], type: s };
              l.url = location.href;
              let d = !1;
              if (s == h.single) {
                let c = document.querySelector(W.selectedVedio),
                  u = "";
                c && (u = this.cleanFileName(c.textContent));
                let h = "";
                try {
                  h = n.asset.stream_urls.Video[0].file;
                } catch (e) {}
                d = n.asset.course_is_drmed;
                let m = "";
                if (
                  (n.asset.media_sources && n.asset.media_sources.length > 0
                    ? (m = n.asset.media_sources[0].src)
                    : "" == h &&
                      ((t = !1),
                      (o =
                        "We only support downloading video courses, not other formats of courses.")),
                  t)
                ) {
                  let c = Z.getCaptionUrl(n.asset);
                  if (
                    ((l = {
                      courseId: e,
                      videoIdList: [n.id],
                      videoUrlList: [h],
                      videoNameList: [u + ".mp4"],
                      captionUrlList: [c],
                      captionNameList: [u + ".vtt"],
                      type: s,
                      secretKey: window.udemyFetcher.secretKey,
                      downloadQueueList: [
                        {
                          lectureId: n.id,
                          videoName: u + ".mp4",
                          webUrl: r,
                          mediaUrl: m,
                        },
                      ],
                    }),
                    null != m && "" != m && d)
                  )
                    if (window.udemyFetcher.secretKey) {
                      if (
                        (i().runtime.sendMessage(l),
                        (a = !0),
                        !(yield D("hasOpenSider")))
                      ) {
                        // Message removed - local activation
                      }
                    } else ((t = !1), (o = "Try to play videos."));
                  else yield i().runtime.sendMessage(l);
                }
              } else if (s == h.bulk) {
                let c = n.videoArr,
                  u = [],
                  h = [],
                  f = [],
                  m = [],
                  g = [],
                  p = [];
                for (let e = 0; e < c.length; e++) {
                  let t = this.cleanFileName(c[e].title);
                  ("" != c[e].url
                    ? (u.push(c[e].id), h.push(c[e].url), f.push(t + ".mp4"))
                    : (d || (d = !0),
                      p.push({
                        lectureId: c[e].id,
                        videoName: t + ".mp4",
                        webUrl: r,
                        mediaUrl: c[e].mediaUrl,
                      })),
                    m.push(c[e].captionUrl),
                    g.push(t + ".vtt"));
                }
                0 == c.length &&
                  0 == p.length &&
                  ((t = !1), (o = "No videos can be download."));
                let y = document.querySelector(W.courseTitle),
                  b = "1111";
                (y && (b = this.cleanFileName(y.textContent)),
                  (l = {
                    courseId: e,
                    courseName: b,
                    videoIdList: u,
                    videoUrlList: h,
                    videoNameList: f,
                    captionUrlList: m,
                    captionNameList: g,
                    type: s,
                    secretKey: window.udemyFetcher.secretKey,
                    downloadQueueList: p,
                  }),
                  d && !window.udemyFetcher.secretKey
                    ? ((t = !1), (o = "Try to play videos."))
                    : (p.length > 0
                        ? (i().runtime.sendMessage(l), (a = !0))
                        : yield i().runtime.sendMessage(l),
                      yield V.downloadCaptionToZip(
                        b + "(captions).zip",
                        m,
                        g,
                      )));
              }
            } catch (e) {
              (console.log(e), (o = "download fail：" + e.toString()));
            } finally {
              if ((this.hiddenButton(r, s), !t)) {
                // Error message removed - local activation
              }
            }
            return a;
          });
        }
        static hiddenButton(e, t) {
          if (t == h.single) {
            let t = e.querySelector(W.postDownloadingBtn);
            (t || (t = e.querySelector(W.postDownloadingWhiteBtn)),
              (t.style.display = "none"));
            let r = e.querySelector(W.postDownloadBtn);
            if (!r) {
              r = e.querySelector(W.postDownloadWhiteBtn);
              const t = e.querySelector(".udemy-download-tab-button");
              t &&
                ((t.style["pointer-events"] = "auto"), (t.style.opacity = "1"));
            }
            r.style.display = "block";
          } else if (t == h.bulk) {
            document
              .getElementById("pldownload-div")
              .classList.remove("pldownloading");
            const e = document.getElementById("pldownload-button");
            if (e) {
              (e.removeAttribute("disabled"),
                (e.style.pointerEvents = "visible"),
                (e.style.opacity = "1"));
              let t = i().i18n.getMessage("mutiUploadTitle");
              e.innerHTML =
                '<span class="udemy-pldownload-save-btn-white"></span>  <span id="pldownload-text">' +
                t +
                "</span>";
            }
          }
        }
        static filterDownloadedVideo(e) {
          return t(this, void 0, void 0, function* () {
            let t = (yield D(E)) || [],
              r = (yield D(O)) || [];
            const n = ((yield D(I)) || []).filter(
                (e) => "downloadSuccess" == e.status,
              ),
              i = new Set([
                ...t.map((e) => e.lectureId),
                ...n.map((e) => e.lectureId),
                ...r.map((e) => e.lectureId),
              ]);
            return e.filter((e) => !i.has(e.id));
          });
        }
        createDownloadButton() {
          return t(this, void 0, void 0, function* () {
            let e = [...document.querySelectorAll(W.leftArea)];
            (0 === e.length && (e = yield this.retryCreateButton()),
              this.creationTimeoutList.forEach((e) => clearTimeout(e)),
              (this.creationTimeoutList = []),
              e.forEach((e) => {
                this.addDownloadButton(e);
              }));
          });
        }
        reinitialize() {
          (this.remove(), this.init());
        }
        init() {
          (console.log("mystudydownloader init..."),
            (this.removed = !1),
            super.init(),
            D("isAutoPlay").then((e) => {
              const t = document.querySelector(
                'button[data-purpose="play-button"]',
              );
              !e && t && (z("isAutoPlay", !0), t.click());
            }));
        }
        remove() {
          ((this.removed = !0), super.remove(".test"));
        }
        retryCreateButton(e = 20, r = 0) {
          return t(this, void 0, void 0, function* () {
            yield new Promise((e) => {
              this.creationTimeoutList.push(setTimeout(e, 100));
            });
            let t = [...document.querySelectorAll(W.leftArea)];
            return (
              (0 === t.length || e <= r) &&
                (this.removed || (t = yield this.retryCreateButton(e, r + 1))),
              t
            );
          });
        }
        addDownloadButton(e) {
          const t = e.querySelector(W.tabBtn);
          let r = i().i18n.getMessage("signalUploadTip"),
            n = i().i18n.getMessage("signalUploadName");
          const s = document.createElement("div");
          (s.classList.add(
            "carousel-module--scroll-item--QZoY7",
            "udemy-download-tab-button",
          ),
            (s.title = r),
            (s.innerHTML =
              '\n            <div class="udemy-post-download-btn-div">\n                <span class="udemy-post-save-btn-white"></span>\n                <span class="udemy-post-saving-btn-white"></span>\n                <span class="ud-btn-label">' +
              n +
              "</span>\n            </div>\n        "),
            (s.onclick = () => {
              const e = t.querySelector(W.postDownloadingWhiteBtn),
                r = t.querySelector(W.postDownloadWhiteBtn);
              ((e.style.display = "block"),
                (r.style.display = "none"),
                (s.style["pointer-events"] = "none"),
                (s.style.opacity = "0.5"),
                V.addDiscardBtn(t),
                V.downloadSigle(t));
            }));
          t.querySelector(".udemy-download-tab-button") || t.append(s);
          const o = document.querySelector(W.rightCloseBtn),
            a = document.createElement("div");
          (a.classList.add("pldownload-div"), (a.id = "pldownload-div"));
          let l = i().i18n.getMessage("mutiUploadTitle"),
            d = i().i18n.getMessage("mutiUploadTip"),
            c = i().i18n.getMessage("mutiUploadingTitle");
          ((a.innerHTML =
            '<button type="button" title="' +
            d +
            '" class="pldownload-button" id="pldownload-button"><span class="udemy-pldownload-save-btn-white"></span>  <span id="pldownload-text">' +
            l +
            "</span></btton>"),
            (a.onclick = () => {
              document
                .getElementById("pldownload-div")
                .classList.add("pldownloading");
              const e = document.getElementById("pldownload-button");
              (e &&
                (e.setAttribute("disabled", "true"),
                (e.style.pointerEvents = "none"),
                (e.style.opacity = "0.5"),
                (e.innerHTML =
                  '<span class="udemy-pldownload-saving-btn-white"></span>  <span id="pldownload-text">' +
                  c +
                  " ...</span>")),
                V.downloadMuti(a));
            }));
          document.querySelector(W.rightTop).querySelector(".pldownload-div") ||
            o.parentNode.insertBefore(a, o);
        }
        static addDiscardBtn(e) {
          return t(this, void 0, void 0, function* () {
            // Discord button removed - local activation
          });
        }
        static extractLectureId(e) {
          const t = new URL(e).pathname.split("/"),
            r = t.findIndex((e) => "lecture" === e);
          return -1 !== r && t.length > r + 1 ? t[r + 1] : null;
        }
        static cleanFileName(e) {
          return e.replace(/[<>:"\/\\|?*\x00-\x1F]/g, "");
        }
        getIframeUrl() {
          return t(this, void 0, void 0, function* () {
            try {
              let e = Z.getElementsDataModuleArgs(W.courseTakeing)[0].courseId,
                t = yield Z.getAllCourseList(e),
                r = t.results.filter(
                  (e) => void 0 !== e.asset && "Video" === e.asset.asset_type,
                ),
                n = V.extractLectureId(location.href),
                i = t.results.filter((e) => e.id == n);
              if (
                i &&
                i.length > 0 &&
                i[0].asset.course_is_drmed &&
                null == i[0].asset.media_license_token
              ) {
                let e = "";
                const t = r.find(
                  (e) =>
                    e.asset.course_is_drmed &&
                    null != e.asset.media_license_token,
                );
                if (t) {
                  e = t.id;
                  const r = location.href.split("/lecture/");
                  let n = `${r[0]}/lecture/${e}`;
                  (yield Z.injetIframe(n), console.info("into iframe..."));
                }
              }
            } catch (e) {}
          });
        }
        static downloadCaptionToZip(e, r, n) {
          return t(this, void 0, void 0, function* () {
            const t = new H();
            for (const [e, i] of r.entries())
              yield V.downloadAndAddToZip(t, i, n[e]);
            t.generateAsync({ type: "blob" }).then((t) => {
              const r = URL.createObjectURL(t),
                n = document.createElement("a");
              ((n.href = r),
                (n.download = e),
                (n.style.display = "none"),
                document.body.appendChild(n),
                n.click(),
                setTimeout(() => URL.revokeObjectURL(r), 1e3),
                n.remove());
            });
          });
        }
        static downloadAndAddToZip(e, r, n) {
          return t(this, void 0, void 0, function* () {
            const t = yield fetch(r),
              i = yield t.text(),
              s = n.replace(/[^\w.-]/g, "_");
            e.file(s, i);
          });
        }
      });
      K = V = e([j], K);
      class q {
        init() {
          i().runtime.onMessage.addListener((e) => {
            if (e.type === f.drmLicense) {
              (window.udemyFetcher || (window.udemyFetcher = {}),
                (window.udemyFetcher.licenseUrl = e.mainUrl));
              let t = Z.getElementsDataModuleArgs(W.courseTakeing)[0].courseId;
              (Z.getDrmSecretKey(t).then((e) => {
                window.udemyFetcher.secretKey = e;
              }),
                Z.removeIframe());
            } else
              e.type === f.retryMessage
                ? U("count")
                : e.type === f.serverError
                  ? R()
                  : x.displayMessage(e);
          });
        }
      }
      class G extends o {
        constructor() {
          (super(),
            (this.url = location.href),
            G.addLocationChangeListener(),
            this.subscribeToLocationChangeListener());
        }
        static isCourse(e) {
          let t =
            /https:\/\/www\.udemy\.com\/course\/[^/]+\/learn\/lecture\/.*(#\w*)?$/.test(
              e,
            );
          return (
            t ||
              (t =
                /https:\/\/[^\/]+\/course\/[^/]+\/learn\/lecture\/.*(#\w*)?$/.test(
                  e,
                )),
            t
          );
        }
        static addLocationChangeListener() {
          (G.injectScript(
            i().runtime.getURL("../assets/libs/inject.js"),
            "body",
          ),
            G.injectScript(
              i().runtime.getURL("../assets/libs/drm_inject.js"),
              "body",
            ),
            G.addProgressBtn());
        }
        static injectScript(e, t) {
          const r = document.getElementsByTagName(t)[0],
            n = document.createElement("script");
          (n.setAttribute("type", "text/javascript"),
            n.setAttribute("src", e),
            r.appendChild(n));
        }
        static addProgressBtn() {
          return t(this, void 0, void 0, function* () {
            let e = yield (function () {
              return t(this, void 0, void 0, function* () {
                let e = !1,
                  t = 0;
                if (!L(location.href)) return { isShow: e, averageProgress: 0 };
                let r = yield D(E);
                return r && 0 != r.length
                  ? (r.forEach((e) => {
                      t += e.percent || 0;
                    }),
                    (e = !0),
                    {
                      isShow: e,
                      averageProgress: parseFloat((t / r.length).toFixed(2)),
                    })
                  : { isShow: e, averageProgress: 0 };
              });
            })();
            if (e.isShow) {
              let t = yield D("hasOpenSider");
              if (document.getElementById("progressDownloadContainer")) {
                const t = parseFloat((3.6 * e.averageProgress).toFixed(2));
                ((document.getElementById("progressTextDownload").innerText =
                  e.averageProgress + "%"),
                  (document.getElementById(
                    "progressCircleDownload",
                  ).style.background =
                    `conic-gradient(#7A93FF ${t}deg, #e0e0e0 ${t}deg 360deg)`));
              } else {
                let r =
                  '\n                    <div id="progressDownload" class="progress-container ' +
                  (t ? "" : "progress-container-sf") +
                  '">\n                        <div id="progressCircleDownload" class="progress-circle">\n                            <span id="progressTextDownload" class="progress-text">' +
                  e.averageProgress +
                  "%</span>\n                        </div>\n                    </div>\n                ";
                const n = document.createElement("div");
                ((n.id = "progressDownloadContainer"),
                  (n.innerHTML = r),
                  document.body.appendChild(n),
                  (n.onclick = () => {
                    let e = i().runtime.connect({ name: "openSidepanels" });
                    (e.postMessage({}),
                      e.disconnect(),
                      (document.getElementById("progressDownload").className =
                        "progress-container"));
                  }));
              }
            }
          });
        }
        static removeProgressBtn() {
          let e = document.getElementById("progressDownloadContainer");
          e && e.remove();
        }
        emitLocationEvent() {
          (G.isCourse(this.url) && this.emit("course"), this.logUrlAction());
        }
        logUrlAction() {
          return t(this, void 0, void 0, function* () {
            if (
              yield (function (e) {
                return t(this, void 0, void 0, function* () {
                  let t = !1;
                  try {
                    ("true" == (yield D("enableRecordUrl")) || L(e)) &&
                      (t = !0);
                  } catch (e) {}
                  return t;
                });
              })(this.url)
            ) {
              let e = {
                courseName: "",
                videoUrlList: [],
                videoNameList: [],
                type: h.changeUrl,
                url: this.url,
              };
              yield i().runtime.sendMessage(e);
            }
          });
        }
        subscribeToLocationChangeListener() {
          window.addEventListener("locationchange", () => {
            this.url !== location.href &&
              ((this.url = location.href), this.emitLocationEvent());
          });
        }
      }
      var Y;
      let Q = (Y = class {
        constructor() {
          ((this.urlChangeEmitter = new G()),
            (this.myStudyDownloader = new K()),
            (this.downloadProgress = new q()),
            new l().subscribe(() => Y.addBackgroundVariable()),
            Y.addBackgroundVariable(),
            Y.adjustForAndroid(),
            this.addListeners(),
            this.urlChangeEmitter.emitLocationEvent(),
            (window.onload = function () {
              Y.addBackgroundVariable();
            }),
            document.addEventListener("pssh_dld", (e) => {
              (window.udemyFetcher || (window.udemyFetcher = {}),
                e.detail && (window.udemyFetcher.PSSH = e.detail));
            }),
            document.addEventListener("clearkey_dld", (e) => {
              (window.udemyFetcher || (window.udemyFetcher = {}),
                (window.udemyFetcher.CLEARKEY = e.detail));
            }),
            document.addEventListener("iframe_pssh_dld", (e) => {
              (window.udemyFetcher || (window.udemyFetcher = {}),
                e.detail && (window.udemyFetcher.PSSH = e.detail));
            }),
            document.addEventListener("iframe_clearkey_dld", (e) => {
              (window.udemyFetcher || (window.udemyFetcher = {}),
                (window.udemyFetcher.CLEARKEY = e.detail));
            }),
            i().storage.onChanged.addListener((e, r) =>
              t(this, void 0, void 0, function* () {
                if ("local" === r)
                  if (e[E]) {
                    const t = e[E].newValue;
                    null != t && t.length > 0
                      ? G.addProgressBtn()
                      : G.removeProgressBtn();
                  } else if (e[I]) {
                    const t = e[I].newValue;
                    if (null != t && t.length > 0) {
                      let e = t[0];
                      if (e.status == y.downloadError) {
                        let t = {
                          title: "UdemyFetcher™",
                          text: "download fail：" + e.msg + (yield F()),
                          mainText: "",
                          mainUrl: "",
                          subText: "",
                          subUrl: "",
                          type: f.pureNotice,
                        };
                        x.displayMessage(t);
                      } else (e.status, y.downloadSuccess);
                    }
                  } else if (e.downloadStatus) {
                    if (e.downloadStatus.newValue) {
                      z("downloadStatus", !1);
                      if (
                        (function () {
                          const e = navigator.userAgent;
                          return e.includes("Edg")
                            ? m.Edge
                            : e.includes("OPR") || e.includes("Opera")
                              ? m.Opera
                              : e.includes("Chrome") && !e.includes("Chromium")
                                ? m.Chrome
                                : !e.includes("Safari") ||
                                    e.includes("Chrome") ||
                                    e.includes("Chromium")
                                  ? e.includes("Firefox")
                                    ? m.Firefox
                                    : m.Unknown
                                  : m.Safari;
                        })() == m.Firefox
                      ) {
                        let e = {
                          title: "UdemyFetcher™",
                          text:
                            "Download has been sent to the queue,Please check in the download center of Firefox (open it by click downloads icon at the top right corner of Firefox)" +
                            (yield F()),
                          mainText: "",
                          mainUrl: "",
                          subText: "",
                          subUrl: "",
                          type: f.pureNotice,
                        };
                        x.displayMessage(e);
                      }
                    }
                  }
              }),
            ),
            z("isAutoPlay", !1));
        }
        static isMobile() {
          return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent,
          );
        }
        static adjustForAndroid() {
          if (Y.isMobile()) {
            const e = document.createElement("style");
            ((e.innerText =
              ".hover-download-button, .account-download-button {    display: none!important;}"),
              document.head.appendChild(e));
          }
        }
        static addBackgroundVariable() {
          const e = document.createElement("style");
          let t =
              "url(" + i().runtime.getURL("assets/imgs/download_red.png") + ")",
            r =
              "url(" + i().runtime.getURL("assets/imgs/loading_red.png") + ")",
            n =
              "url(" +
              i().runtime.getURL("assets/imgs/download_yellow.png") +
              ")",
            s =
              "url(" +
              i().runtime.getURL("assets/imgs/download_white.png") +
              ")",
            o =
              "url(" +
              i().runtime.getURL("assets/imgs/loading_white.png") +
              ")",
            a =
              "url(" +
              i().runtime.getURL("assets/imgs/discard-white.png") +
              ")",
            l =
              "url(" +
              i().runtime.getURL("assets/imgs/discard-green.png") +
              ")";
          ((e.innerHTML = `\n            :root {\n                --addon-save-red: ${t};\n                --addon-saving-red: ${r};\n                --addon-save-yellow: ${n};\n                --addon-save-white: ${s};\n                --addon-saving-white: ${o};\n                --addon-discard-white: ${a};\n                --addon-discard-green: ${l};\n            }\n        `),
            document.head.appendChild(e));
        }
        addListeners() {
          (this.downloadProgress.init(),
            this.urlChangeEmitter.on("course", () => {
              (this.deleteAllDownloader(),
                this.myStudyDownloader.init(),
                this.myStudyDownloader.getIframeUrl());
            }));
        }
        deleteAllDownloader() {
          this.myStudyDownloader.remove();
        }
      });
      ((Q = Y = e([N], Q)), new Q());
    })());
})();
