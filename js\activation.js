/**
 * CIH99 Remote Activation System
 * Handles license verification and activation for the browser extension
 */

class CIH99Activation {
    constructor() {
        this.ACTIVATION_URL = 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json';
        this.STORAGE_KEY = 'CIH99_ACTIVATION_STATUS';
        this.LAST_CHECK_KEY = 'CIH99_LAST_CHECK';
        this.CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes
        this.isActivated = false;
        this.currentPassword = null;
        
        // Initialize activation check
        this.init();
    }

    /**
     * Initialize the activation system
     */
    async init() {
        try {
            // Check stored activation status
            const stored = await this.getStoredActivation();
            if (stored && stored.isActivated) {
                this.isActivated = true;
                this.currentPassword = stored.password;
                
                // Verify activation is still valid
                await this.verifyActivation();
            }
            
            // Set up periodic checks
            this.setupPeriodicCheck();
        } catch (error) {
            console.error('CIH99 Activation Init Error:', error);
            this.isActivated = false;
        }
    }

    /**
     * Check activation code against remote server
     */
    async checkActivation(inputCode) {
        try {
            const response = await fetch(this.ACTIVATION_URL, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to connect to activation server');
            }

            const data = await response.json();
            const validCode = data.current_password;

            if (inputCode === validCode) {
                this.isActivated = true;
                this.currentPassword = validCode;
                
                // Store activation status
                await this.storeActivation(validCode);
                
                return { success: true, message: 'Activation successful' };
            } else {
                this.isActivated = false;
                await this.clearActivation();
                return { success: false, message: 'Invalid activation code' };
            }
        } catch (error) {
            console.error('CIH99 Activation Check Error:', error);
            return { success: false, message: 'Connection error: ' + error.message };
        }
    }

    /**
     * Verify current activation is still valid
     */
    async verifyActivation() {
        if (!this.currentPassword) {
            this.isActivated = false;
            return false;
        }

        try {
            const response = await fetch(this.ACTIVATION_URL, {
                method: 'GET',
                cache: 'no-cache'
            });

            if (response.ok) {
                const data = await response.json();
                const validCode = data.current_password;
                
                if (this.currentPassword === validCode) {
                    this.isActivated = true;
                    await this.updateLastCheck();
                    return true;
                } else {
                    // Password changed, deactivate
                    this.isActivated = false;
                    await this.clearActivation();
                    return false;
                }
            } else {
                // Server error, keep current status but log
                console.warn('CIH99: Could not verify activation - server error');
                return this.isActivated;
            }
        } catch (error) {
            console.warn('CIH99: Could not verify activation - network error:', error);
            return this.isActivated; // Keep current status on network error
        }
    }

    /**
     * Check if extension is activated
     */
    isExtensionActivated() {
        return this.isActivated;
    }

    /**
     * Require activation before executing function
     */
    requireActivation(callback, errorCallback = null) {
        if (this.isActivated) {
            return callback();
        } else {
            const error = 'Extension not activated. Please enter valid activation code.';
            console.warn('CIH99:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                throw new Error(error);
            }
        }
    }

    /**
     * Store activation status in extension storage
     */
    async storeActivation(password) {
        const data = {
            isActivated: true,
            password: password,
            timestamp: Date.now()
        };
        
        if (typeof chrome !== 'undefined' && chrome.storage) {
            await chrome.storage.local.set({ [this.STORAGE_KEY]: data });
        } else {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
        }
    }

    /**
     * Get stored activation status
     */
    async getStoredActivation() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.local.get(this.STORAGE_KEY);
                return result[this.STORAGE_KEY] || null;
            } else {
                const stored = localStorage.getItem(this.STORAGE_KEY);
                return stored ? JSON.parse(stored) : null;
            }
        } catch (error) {
            console.error('CIH99: Error getting stored activation:', error);
            return null;
        }
    }

    /**
     * Clear activation status
     */
    async clearActivation() {
        this.isActivated = false;
        this.currentPassword = null;
        
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.remove([this.STORAGE_KEY, this.LAST_CHECK_KEY]);
            } else {
                localStorage.removeItem(this.STORAGE_KEY);
                localStorage.removeItem(this.LAST_CHECK_KEY);
            }
        } catch (error) {
            console.error('CIH99: Error clearing activation:', error);
        }
    }

    /**
     * Update last check timestamp
     */
    async updateLastCheck() {
        const timestamp = Date.now();
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.set({ [this.LAST_CHECK_KEY]: timestamp });
            } else {
                localStorage.setItem(this.LAST_CHECK_KEY, timestamp.toString());
            }
        } catch (error) {
            console.error('CIH99: Error updating last check:', error);
        }
    }

    /**
     * Setup periodic activation verification
     */
    setupPeriodicCheck() {
        setInterval(async () => {
            if (this.isActivated) {
                await this.verifyActivation();
            }
        }, this.CHECK_INTERVAL);
    }

    /**
     * Deactivate extension
     */
    async deactivate() {
        await this.clearActivation();
        console.log('CIH99: Extension deactivated');
    }
}

// Create global instance
const CIH99_ACTIVATION = new CIH99Activation();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CIH99Activation;
}
