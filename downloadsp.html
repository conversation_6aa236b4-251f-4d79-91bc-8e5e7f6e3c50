<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>CIH99 Video Downloader - Downloads Manager</title>
    <style>
        body {
            font-family: Microsoft YaHei;
            margin: 10px;
            min-width: 320px;
        }
        h3 {
            margin: 0;
            padding: 0;
            font-size: inherit;
            font-weight: normal;
            line-height: normal;
            color: inherit;
            font-size: 14px;
            font-weight: 600;
        }

        .title{
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding: 5px;
        }
        
        .title button{
            margin: 0 0 0 auto;
            border: none;
            cursor: pointer;
            position: absolute;
            left: 10px;
            background: #F5F4E3;
            font-size: 13px;
        }
        
        .title .right-records{
            position: absolute;
            right: 13px;
            font-size: 13px;
        }

        .download-config,.downloading-container,.download-container{
            margin-bottom: 10px;
        }
        
        .download-config .toggle{
            display: inline-block;
        }

        .download-config .toggle .tip{
            position: absolute;
            margin-left: 10px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 100px;
            height: 30px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            font-size: 14px;
            color: white;
            font-weight: bold;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #1E90FF;
        }
        
        input:checked + .slider:before {
            transform: translateX(70px);
        }
        
        .slider .text-on {
            display: none;
        }
        
        input:checked + .slider .text-on {
            display: block;
        }
        
        input:checked + .slider .text-off {
            display: none;
        }
        
        .progress-container-s {
            margin-bottom: 10px;
        }
        .progress-label {
            font-size: 14px;
            position: absolute;
            top: 0;
            left: 0;
            width: calc(100% - 70px);
            line-height: 30px;
            background: transparent;
            z-index: 1;
            padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .download-label {
            font-size: 14px;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            line-height: 30px;
            background: transparent;
            z-index: 1;
            padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .downloaded-label {
            font-size: 14px;
            position: absolute;
            top: 0;
            left: 0;
            width: calc(100% - 140px);
            line-height: 30px;
            background: transparent;
            z-index: 1;
            padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .downloaded-item-content{
            display: inline-flex;
            align-items: center;
            width: 100%;
        }
        .downloaded-item-content-left{
            flex: auto;
            height: 30px;
            background-color: #B8EBEE;
            border-radius: 5px;
            position: relative;
            color: #706363;
        }
        .progress-bar-wrapper {
            display: flex;
            align-items: center;
            flex: auto;
        }
        .progress-bar {
            flex: 1;
            border-radius: 5px;
            overflow: hidden;
            height: 30px;
            position: relative;
            border: 1px solid #11C7F9;
            color: #0A2C40;
        }
        .progress-bar div {
            height: 100%;
        }
        .blue {
            background-color: #11C7F9;
            width: 0%;
        }
        .red {
            background-color: #ff0000;
            width: 90%;
        }
        .green {
            background-color: #28a745;
            width: 100%;
        }
        .progress-percentage {
            font-size: 12px;
            margin-left: 10px;
            white-space: nowrap;
            line-height: 30px;
            width: 50px;
            position: absolute;
            right: 0;
        }
        .tipmsg{
            font-size: 12px;
        }

        .downloadMethod{
            font-size: 12px;
            margin-top: 5px;
        }

        .downloaded-item {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .downloaded-item p{
            margin: 5px 0;
        }

        .downloaded-item .secondp{
            font-size: 12px;
            display: flex;
            position: absolute;
            right: 5px;
            line-height: 30px;
            width: 125px;
            justify-content: flex-end;
        }
        
        .downloaded-item .secondp .status-success {
            color: green;
            font-weight: bold;
            margin: 0 0 0 auto;
        }
        
        .downloaded-item .secondp .status-fail {
            color: red;
            font-weight: bold;
            margin: 0 0 0 auto;
        }

        .downloaded-item .errormsg{
            font-size: 12px;
            color: red;
        }

        .downloaded-item .downloadMethod{
            font-size: 12px;
            margin-top: 5px;
        }

        .downloaded-item .tipmsg{
            font-size: 12px;
            color: #2d8cf0;
        }

        .downloaded-item .titlea{
            cursor: pointer;
            color: #2d8cf0;
            font-size: 14px;
            text-decoration: none;
        }

        .nodata{
            display: flex;
            padding: 10px 10px 15px;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .downloading-cancle{
            display: flex;
            align-items: center;
            margin: 0 0 0 5px;
        }
        
        .downloading-cancle img{
            width: 25px;
            cursor: pointer;
        }
        
        .cancleButton{
            margin: 0 0 0 auto;
            border: none;
            color: #fff;
            background-color: #ed4014;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .quota-section {
            background-color: #e8f5e8;
            border: 2px solid #4CAF50;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 10px;
            min-width: 100%;
            box-sizing: border-box;
        }
        
        .alldownloadDiv{
            background-color: #F5F4E3;
            padding: 10px 0;
            border-radius: 10px;
        }
        
        #downloadDiv, #downloadedDiv{
            background-color: #DED6D6;
            border-radius: 10px;
            padding: 10px;
        }
        
        #downloadingDiv{
            padding: 10px;
        }
               
        .downloaded-tip{
            font-size: 12px;
            margin-top: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #817C7C;
        }

        #download-container,#downloaded-container {
            display: none;
        }
    </style>
</head>
<body>
    <div class="quota-section">
        <h3 style="color: #2e7d32; margin: 0 0 10px 0;">🚀 CIH99 Professional Video Downloader</h3>
        <p style="color: #388e3c; font-size: 16px; margin: 0;">✨ Unlimited Downloads • 🎯 Highest Quality • ⚡ Lightning Fast</p>
    </div>

    <div class="alldownloadDiv">
        <div class="downloading-container">
            <div class="title">
                <h3>DOWNLOADING</h3>
            </div>
            <div id="downloadingDiv">
                <span class="nodata">No data being downloaded</span>
            </div>
        </div>

        <div id="download-container" class="download-container">
            <div class="title">
                <button id="clearDownload">cancel all</button>
                <h3>DOWNLOAD WAITING</h3>
                <span class="right-records"><span id="downloadRecords">0</span> records</span>
            </div>
            <div id="downloadDiv"></div>
            <div id="download-pagination"></div>
        </div>

        <div id="downloaded-container" class="downloaded-container">
            <div class="title">
                <h3>DOWNLOADED</h3>
                <span class="right-records"><span id="downloadedRecords">0</span> records</span>
            </div>
            <div id="downloadedDiv"></div>
            <div id="downloaded-pagination"></div>
            <div class="downloaded-tip">Only display the downloaded records today</div>
        </div>
    </div>

    <script src="js/sidepanel.js"></script>
</body>
</html>
