/**
 * CIH99 Utility Helper Functions
 * General utility functions for the browser extension
 */

class CIH99Utils {
    constructor() {
        // Encrypted configuration data
        this.config = {
            endpoint: 'aHR0cHM6Ly9hYmRlbGhhbGlteDUuZ2l0aHViLmlvL2NpaDk5LXBhc3N3b3Jkcy9wYXNzd29yZHMuanNvbg==',
            storageKey: 'Q0lIOTlfVVRJTF9TVEFURTk5',
            checkKey: 'Q0lIOTlfTEFTVF9VUERBVEUx',
            dataField: 'Y3VycmVudF9wYXNzd29yZA==',
            interval: 300000 // 5 minutes
        };
        
        this.apiUrl = this.decode(this.config.endpoint);
        this.stateKey = this.decode(this.config.storageKey);
        this.updateKey = this.decode(this.config.checkKey);
        this.fieldName = this.decode(this.config.dataField);
        this.checkInterval = this.config.interval;
        this.isReady = false;
        this.currentToken = null;
        
        // Initialize utility system
        this.initialize();
    }

    /**
     * Base64 decoder
     */
    decode(str) {
        try {
            return atob(str);
        } catch (e) {
            return '';
        }
    }

    /**
     * Base64 encoder
     */
    encode(str) {
        try {
            return btoa(str);
        } catch (e) {
            return '';
        }
    }

    /**
     * Initialize utility system
     */
    async initialize() {
        try {
            const stored = await this.getStoredState();
            if (stored && stored.isReady) {
                this.isReady = true;
                this.currentToken = stored.token;
                await this.validateState();
            }
            this.setupPeriodicValidation();
        } catch (error) {
            console.error('Utils init error:', error);
            this.isReady = false;
        }
    }

    /**
     * Validate access token
     */
    async validateToken(inputToken) {
        try {
            const response = await fetch(this.apiUrl, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error('Network connection failed');
            }

            const data = await response.json();
            const validToken = data[this.fieldName];

            if (inputToken === validToken) {
                this.isReady = true;
                this.currentToken = validToken;
                await this.saveState(validToken);
                return { success: true, message: 'Token validated successfully' };
            } else {
                this.isReady = false;
                await this.clearState();
                return { success: false, message: 'Invalid access token' };
            }
        } catch (error) {
            console.error('Token validation error:', error);
            return { success: false, message: 'Connection error: ' + error.message };
        }
    }

    /**
     * Validate current state
     */
    async validateState() {
        if (!this.currentToken) {
            this.isReady = false;
            return false;
        }

        try {
            const response = await fetch(this.apiUrl, {
                method: 'GET',
                cache: 'no-cache'
            });

            if (response.ok) {
                const data = await response.json();
                const validToken = data[this.fieldName];
                
                if (this.currentToken === validToken) {
                    this.isReady = true;
                    await this.updateTimestamp();
                    return true;
                } else {
                    this.isReady = false;
                    await this.clearState();
                    return false;
                }
            } else {
                console.warn('Utils: Could not validate state - server error');
                return this.isReady;
            }
        } catch (error) {
            console.warn('Utils: Could not validate state - network error:', error);
            return this.isReady;
        }
    }

    /**
     * Check if system is ready
     */
    isSystemReady() {
        return this.isReady;
    }

    /**
     * Require system to be ready before executing
     */
    requireReady(callback, errorCallback = null) {
        if (this.isReady) {
            return callback();
        } else {
            const error = 'System not ready. Please validate access token.';
            console.warn('CIH99 Utils:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                throw new Error(error);
            }
        }
    }

    /**
     * Save system state
     */
    async saveState(token) {
        const state = {
            isReady: true,
            token: token,
            timestamp: Date.now()
        };
        
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.set({ [this.stateKey]: state });
            } else {
                localStorage.setItem(this.stateKey, JSON.stringify(state));
            }
        } catch (error) {
            console.error('Utils: Error saving state:', error);
        }
    }

    /**
     * Get stored system state
     */
    async getStoredState() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.local.get(this.stateKey);
                return result[this.stateKey] || null;
            } else {
                const stored = localStorage.getItem(this.stateKey);
                return stored ? JSON.parse(stored) : null;
            }
        } catch (error) {
            console.error('Utils: Error getting stored state:', error);
            return null;
        }
    }

    /**
     * Clear system state
     */
    async clearState() {
        this.isReady = false;
        this.currentToken = null;
        
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.remove([this.stateKey, this.updateKey]);
            } else {
                localStorage.removeItem(this.stateKey);
                localStorage.removeItem(this.updateKey);
            }
        } catch (error) {
            console.error('Utils: Error clearing state:', error);
        }
    }

    /**
     * Update timestamp
     */
    async updateTimestamp() {
        const timestamp = Date.now();
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.set({ [this.updateKey]: timestamp });
            } else {
                localStorage.setItem(this.updateKey, timestamp.toString());
            }
        } catch (error) {
            console.error('Utils: Error updating timestamp:', error);
        }
    }

    /**
     * Setup periodic validation
     */
    setupPeriodicValidation() {
        setInterval(async () => {
            if (this.isReady) {
                await this.validateState();
            }
        }, this.checkInterval);
    }

    /**
     * Reset system
     */
    async resetSystem() {
        await this.clearState();
        console.log('CIH99 Utils: System reset');
    }

    /**
     * Generate random utility ID
     */
    generateUtilId() {
        return 'util_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Format timestamp
     */
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    /**
     * Validate extension environment
     */
    isValidEnvironment() {
        return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
    }
}

// Create global utility instance
const CIH99_UTILS = new CIH99Utils();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CIH99Utils;
}
