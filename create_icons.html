<!DOCTYPE html>
<html>
<head>
    <title>CIH99 Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #2E7D32; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #1B5E20; }
    </style>
</head>
<body>
    <h1>CIH99 Icon Generator</h1>
    <p>Click the buttons below to generate and download CIH99 icons in different sizes:</p>
    
    <div class="icon-container">
        <button onclick="generateIcon(16)">Generate 16x16</button>
        <button onclick="generateIcon(32)">Generate 32x32</button>
        <button onclick="generateIcon(48)">Generate 48x48</button>
        <button onclick="generateIcon(64)">Generate 64x64</button>
        <button onclick="generateIcon(128)">Generate 128x128</button>
        <button onclick="generateIcon(256)">Generate 256x256</button>
    </div>
    
    <div id="canvases"></div>

    <script>
        function generateIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background Circle
            ctx.fillStyle = '#2E7D32';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Border
            ctx.strokeStyle = '#1B5E20';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // CIH99 Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size/7}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('CIH99', size/2, size/2.5);
            
            // Download Arrow
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(2, size/40);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // Vertical line
            ctx.beginPath();
            ctx.moveTo(size/2, size/1.9);
            ctx.lineTo(size/2, size/1.4);
            ctx.stroke();
            
            // Arrow head
            ctx.beginPath();
            ctx.moveTo(size/2 - size/12, size/1.5);
            ctx.lineTo(size/2, size/1.4);
            ctx.lineTo(size/2 + size/12, size/1.5);
            ctx.stroke();
            
            // Video Icon
            const videoWidth = size/4.5;
            const videoHeight = size/7;
            const videoX = size/2 - videoWidth/2;
            const videoY = size/1.25;
            
            ctx.fillStyle = 'white';
            ctx.fillRect(videoX, videoY, videoWidth, videoHeight);
            
            // Play button
            ctx.fillStyle = '#2E7D32';
            ctx.beginPath();
            ctx.moveTo(videoX + videoWidth/3, videoY + videoHeight/4);
            ctx.lineTo(videoX + videoWidth/3, videoY + 3*videoHeight/4);
            ctx.lineTo(videoX + 2*videoWidth/3, videoY + videoHeight/2);
            ctx.closePath();
            ctx.fill();
            
            // Add to page
            const container = document.getElementById('canvases');
            const div = document.createElement('div');
            div.innerHTML = `<h3>${size}x${size}</h3>`;
            div.appendChild(canvas);
            
            // Download button
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = `Download ${size}x${size}`;
            downloadBtn.onclick = () => downloadCanvas(canvas, `cih99_icon_${size}.png`);
            div.appendChild(downloadBtn);
            
            container.appendChild(div);
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate all sizes on load
        window.onload = () => {
            [16, 32, 48, 64, 128, 256].forEach(size => generateIcon(size));
        };
    </script>
</body>
</html>
