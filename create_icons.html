<!DOCTYPE html>
<html>
<head>
    <title>CIH99 Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #2E7D32; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #1B5E20; }
        .activation-container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .activation-input {
            padding: 10px;
            margin: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            width: 300px;
        }
        .error-message {
            color: #d32f2f;
            margin: 10px 0;
            font-weight: bold;
        }
        .success-message {
            color: #2E7D32;
            margin: 10px 0;
            font-weight: bold;
        }
        .loading {
            color: #1976d2;
            margin: 10px 0;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>CIH99 Icon Generator</h1>

    <!-- Activation Section -->
    <div id="activationSection" class="activation-container">
        <h2>🔐 تفعيل الأداة</h2>
        <p>يرجى إدخال كود التفعيل للمتابعة:</p>
        <input type="text" id="activationCode" class="activation-input" placeholder="أدخل كود التفعيل هنا" />
        <br>
        <button onclick="checkActivation()">تفعيل</button>
        <div id="activationMessage"></div>
        <div id="loadingMessage" class="loading hidden">جاري التحقق من كود التفعيل...</div>
    </div>

    <!-- Main Content (Hidden until activated) -->
    <div id="mainContent" class="hidden">
        <p>Click the buttons below to generate and download CIH99 icons in different sizes:</p>

        <div class="icon-container">
            <button onclick="generateIcon(16)">Generate 16x16</button>
            <button onclick="generateIcon(32)">Generate 32x32</button>
            <button onclick="generateIcon(48)">Generate 48x48</button>
            <button onclick="generateIcon(64)">Generate 64x64</button>
            <button onclick="generateIcon(128)">Generate 128x128</button>
            <button onclick="generateIcon(256)">Generate 256x256</button>
        </div>

        <div id="canvases"></div>
    </div>

    <script>
        // Remote activation system
        const ACTIVATION_URL = 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json';
        let isActivated = false;

        // Check activation code against remote server
        async function checkActivation() {
            const inputCode = document.getElementById('activationCode').value.trim();
            const messageDiv = document.getElementById('activationMessage');
            const loadingDiv = document.getElementById('loadingMessage');

            if (!inputCode) {
                messageDiv.innerHTML = '<div class="error-message">يرجى إدخال كود التفعيل</div>';
                return;
            }

            // Show loading
            loadingDiv.classList.remove('hidden');
            messageDiv.innerHTML = '';

            try {
                const response = await fetch(ACTIVATION_URL);
                if (!response.ok) {
                    throw new Error('فشل في الاتصال بالخادم');
                }

                const data = await response.json();
                const validCode = data.current_password;

                if (inputCode === validCode) {
                    // Activation successful
                    isActivated = true;
                    messageDiv.innerHTML = '<div class="success-message">✅ تم التفعيل بنجاح!</div>';

                    // Hide activation section and show main content
                    setTimeout(() => {
                        document.getElementById('activationSection').classList.add('hidden');
                        document.getElementById('mainContent').classList.remove('hidden');
                        // Generate all icons automatically after activation
                        [16, 32, 48, 64, 128, 256].forEach(size => generateIcon(size));
                    }, 1500);

                } else {
                    messageDiv.innerHTML = '<div class="error-message">❌ كود التفعيل غير صحيح</div>';
                }

            } catch (error) {
                messageDiv.innerHTML = '<div class="error-message">❌ خطأ في الاتصال: ' + error.message + '</div>';
            } finally {
                loadingDiv.classList.add('hidden');
            }
        }

        // Allow Enter key to trigger activation
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('activationCode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkActivation();
                }
            });
        });
        function generateIcon(size) {
            // Check if activated before generating
            if (!isActivated) {
                alert('يرجى تفعيل الأداة أولاً');
                return;
            }

            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Background Circle
            ctx.fillStyle = '#2E7D32';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();

            // Border
            ctx.strokeStyle = '#1B5E20';
            ctx.lineWidth = 2;
            ctx.stroke();

            // CIH99 Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size/7}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText('CIH99', size/2, size/2.5);

            // Download Arrow
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(2, size/40);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // Vertical line
            ctx.beginPath();
            ctx.moveTo(size/2, size/1.9);
            ctx.lineTo(size/2, size/1.4);
            ctx.stroke();

            // Arrow head
            ctx.beginPath();
            ctx.moveTo(size/2 - size/12, size/1.5);
            ctx.lineTo(size/2, size/1.4);
            ctx.lineTo(size/2 + size/12, size/1.5);
            ctx.stroke();

            // Video Icon
            const videoWidth = size/4.5;
            const videoHeight = size/7;
            const videoX = size/2 - videoWidth/2;
            const videoY = size/1.25;

            ctx.fillStyle = 'white';
            ctx.fillRect(videoX, videoY, videoWidth, videoHeight);

            // Play button
            ctx.fillStyle = '#2E7D32';
            ctx.beginPath();
            ctx.moveTo(videoX + videoWidth/3, videoY + videoHeight/4);
            ctx.lineTo(videoX + videoWidth/3, videoY + 3*videoHeight/4);
            ctx.lineTo(videoX + 2*videoWidth/3, videoY + videoHeight/2);
            ctx.closePath();
            ctx.fill();

            // Add to page
            const container = document.getElementById('canvases');
            const div = document.createElement('div');
            div.innerHTML = `<h3>${size}x${size}</h3>`;
            div.appendChild(canvas);

            // Download button
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = `Download ${size}x${size}`;
            downloadBtn.onclick = () => downloadCanvas(canvas, `cih99_icon_${size}.png`);
            div.appendChild(downloadBtn);

            container.appendChild(div);
        }

        function downloadCanvas(canvas, filename) {
            // Check if activated before downloading
            if (!isActivated) {
                alert('يرجى تفعيل الأداة أولاً');
                return;
            }

            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Periodic activation check (every 5 minutes)
        setInterval(async function() {
            if (isActivated) {
                try {
                    const response = await fetch(ACTIVATION_URL);
                    if (response.ok) {
                        const data = await response.json();
                        // You can add additional checks here if needed
                        console.log('Activation status verified');
                    }
                } catch (error) {
                    console.log('Could not verify activation status');
                }
            }
        }, 300000); // 5 minutes

        // Remove auto-generation on load - now only happens after activation
        window.onload = () => {
            // Show activation section by default
            console.log('CIH99 Icon Generator loaded - activation required');
        };
    </script>
</body>
</html>
