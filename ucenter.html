<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>CIH99 - Video Downloader Tool</title><style>body {
      font-family: Arial, sans-serif;
      background-color: #EFEBE0;
      color: #333;
      text-align: center;
      /* padding: 15px; */
      width: 550px;
      overflow-x: hidden;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      height: auto;
    }
    h1 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    p {
      font-size: 14px;
      margin: 5px 0;
      color: #555;
    }
    .video-placeholder {
      display: inline-flex;
      align-items: center;

    }

    .video-placeholder img{
      width: 100%;
      margin: auto;
    }
    /* Removed unused button styles */
    .small-text {
      font-size: 14px;
      margin-top: 10px;
    }

    .bottom-text{
      margin-top: 30px;
    }

    /* Removed unused icon styles */

    #userId{
      font-size: 16px;
      font-weight: 700;
    }

    /* .buttonSpan{
      width: 140px;
    } */

    .sj-tip{
      font-size: 17px;
      color: red;
     }

     #GuestDiv,#MemberDiv,#ProMemberDiv{
      display: none !important;
     }

     /* CIH99 Activation Styles */
     .activation-container {
       background: #fff;
       border: 2px solid #2E7D32;
       border-radius: 10px;
       padding: 20px;
       margin: 20px 0;
       box-shadow: 0 4px 8px rgba(0,0,0,0.1);
     }
     .activation-input {
       width: 80%;
       padding: 12px;
       margin: 10px 0;
       border: 2px solid #ddd;
       border-radius: 5px;
       font-size: 16px;
       text-align: center;
     }
     .activation-input:focus {
       border-color: #2E7D32;
       outline: none;
     }
     .activation-btn {
       background: #2E7D32;
       color: white;
       border: none;
       padding: 12px 30px;
       border-radius: 5px;
       font-size: 16px;
       cursor: pointer;
       margin: 10px 5px;
     }
     .activation-btn:hover {
       background: #1B5E20;
     }
     .activation-btn:disabled {
       background: #ccc;
       cursor: not-allowed;
     }
     .error-message {
       color: #d32f2f;
       margin: 10px 0;
       font-weight: bold;
       background: #ffebee;
       padding: 10px;
       border-radius: 5px;
     }
     .success-message {
       color: #2E7D32;
       margin: 10px 0;
       font-weight: bold;
       background: #e8f5e8;
       padding: 10px;
       border-radius: 5px;
     }
     .loading {
       color: #1976d2;
       margin: 10px 0;
     }
     .hidden {
       display: none !important;
     }
     .activation-status {
       background: #e8f5e8;
       border: 1px solid #4CAF50;
       border-radius: 5px;
       padding: 10px;
       margin: 10px 0;
       color: #2e7d32;
     }</style></head><body><div class="container">

    <!-- System Setup Section -->
    <div id="setupSection" class="activation-container">
      <div class="video-placeholder"><img src="../assets/imgs/optionlogo.png"/></div>
      <h1 style="color: #2E7D32; font-size: 24px; margin: 20px 0;">⚙️ CIH99 Extension Setup</h1>
      <p style="color: #666; font-size: 16px;">Please enter your access token to initialize the extension:</p>
      <input type="text" id="accessToken" class="activation-input" placeholder="Enter access token here" />
      <br>
      <button id="setupBtn" class="activation-btn" onclick="initializeExtension()">Initialize Extension</button>
      <div id="setupMessage"></div>
      <div id="loadingMessage" class="loading hidden">Validating access token...</div>
    </div>

    <!-- Main Content (Hidden until activated) -->
    <div id="mainContent" class="hidden">
      <div class="video-placeholder"><img src="../assets/imgs/optionlogo.png"/></div>
      <h1 style="color: #2E7D32; font-size: 24px; margin: 20px 0;">🚀 CIH99 Video Downloader</h1>
      <div class="activation-status">
        ✅ Extension Initialized Successfully
      </div>
      <h2 style="color: #c733a5; font-size: 16px;">Professional Course Video Downloader - Please refresh the page if you can't see the download button.</h2>
      <p class="small-text bottom-text">Your ID:<span id="userId"></span><br/><strong>© 2025 CIH99 - All Rights Reserved</strong></p>
      <div style="margin: 1rem 0;">
        <div style="text-align: center; padding: 20px; background-color: #e8f5e8; border-radius: 10px; border: 2px solid #4CAF50;">
          <h3 style="color: #2e7d32; margin: 0 0 10px 0;">🎉 CIH99 Professional Video Downloader</h3>
          <p style="color: #388e3c; font-size: 16px; margin: 0;">✨ Unlimited Downloads • 🎯 Highest Quality • ⚡ Lightning Fast<br/>
          <strong>Download unlimited course videos without any restrictions!</strong></p>
        </div>
      </div>

      <!-- Reset Button -->
      <div style="margin-top: 20px;">
        <button class="activation-btn" style="background: #d32f2f;" onclick="resetExtension()">Reset Extension</button>
      </div>
    </div>
</div>

<script src="js/utils_helper.js"></script>
<script>
// CIH99 Extension UI Logic
let utilsInstance = null;

document.addEventListener('DOMContentLoaded', async function() {
    // Wait for utils system to initialize and check if it exists
    setTimeout(async () => {
        if (typeof CIH99_UTILS !== 'undefined') {
            utilsInstance = CIH99_UTILS;
            await checkSystemStatus();
        } else {
            console.error('CIH99_UTILS not loaded');
            showSetupScreen();
        }
    }, 200);
});

async function checkSystemStatus() {
    try {
        if (utilsInstance && utilsInstance.isSystemReady()) {
            showMainContent();
        } else {
            showSetupScreen();
        }
    } catch (error) {
        console.error('Error checking system status:', error);
        showSetupScreen();
    }
}

function showSetupScreen() {
    document.getElementById('setupSection').classList.remove('hidden');
    document.getElementById('mainContent').classList.add('hidden');
}

function showMainContent() {
    document.getElementById('setupSection').classList.add('hidden');
    document.getElementById('mainContent').classList.remove('hidden');
}

async function initializeExtension() {
    const inputToken = document.getElementById('accessToken').value.trim();
    const messageDiv = document.getElementById('setupMessage');
    const loadingDiv = document.getElementById('loadingMessage');
    const setupBtn = document.getElementById('setupBtn');

    if (!inputToken) {
        messageDiv.innerHTML = '<div class="error-message">Please enter access token</div>';
        return;
    }

    // Show loading
    loadingDiv.classList.remove('hidden');
    messageDiv.innerHTML = '';
    setupBtn.disabled = true;

    try {
        const result = await utilsInstance.validateToken(inputToken);

        if (result.success) {
            messageDiv.innerHTML = '<div class="success-message">✅ Extension initialized successfully!</div>';
            setTimeout(() => {
                showMainContent();
                // Reload option.js functionality
                if (typeof window.loadOptionScript === 'function') {
                    window.loadOptionScript();
                }
            }, 1500);
        } else {
            messageDiv.innerHTML = '<div class="error-message">❌ ' + result.message + '</div>';
        }
    } catch (error) {
        messageDiv.innerHTML = '<div class="error-message">❌ Error: ' + error.message + '</div>';
    } finally {
        loadingDiv.classList.add('hidden');
        setupBtn.disabled = false;
    }
}

async function resetExtension() {
    if (confirm('Are you sure you want to reset the extension?')) {
        await utilsInstance.resetSystem();
        showSetupScreen();
        document.getElementById('accessToken').value = '';
        document.getElementById('setupMessage').innerHTML = '<div class="error-message">Extension reset</div>';
    }
}

// Allow Enter key to trigger initialization
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !document.getElementById('setupSection').classList.contains('hidden')) {
        initializeExtension();
    }
});
</script>
<script src="js/option.js"></script>
</body></html>